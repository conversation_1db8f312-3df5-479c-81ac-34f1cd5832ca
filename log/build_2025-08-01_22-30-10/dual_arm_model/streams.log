[0.021s] Invoking command in '/home/<USER>/test_ws/build/dual_arm_model': CMAKE_PREFIX_PATH=/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake /home/<USER>/test_ws/dual_ur5/src/dual_arm_model -DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/dual_arm_model
[0.362s] -- The C compiler identification is GNU 12.3.0
[0.362s] -- The CXX compiler identification is GNU 12.3.0
[0.362s] -- Detecting C compiler ABI info
[0.362s] -- Detecting C compiler ABI info - done
[0.362s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.362s] -- Detecting C compile features
[0.362s] -- Detecting C compile features - done
[0.362s] -- Detecting CXX compiler ABI info
[0.362s] -- Detecting CXX compiler ABI info - done
[0.362s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.362s] -- Detecting CXX compile features
[0.362s] -- Detecting CXX compile features - done
[0.362s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.648s] -- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter 
[0.730s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.774s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.778s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.785s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.793s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.803s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.846s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.848s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.914s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.932s] -- Found FastRTPS: /opt/ros/humble/include  
[0.960s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.969s] -- Looking for pthread.h
[1.024s] -- Looking for pthread.h - found
[1.025s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.079s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.080s] -- Found Threads: TRUE  
[1.117s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.135s] Traceback (most recent call last):
[1.135s]   File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>
[1.135s]     from catkin_pkg.package import parse_package_string
[1.135s] ModuleNotFoundError: No module named 'catkin_pkg'
[1.137s] [31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):
[1.137s]   execute_process(/home/<USER>/anaconda3/bin/python3
[1.137s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py
[1.137s]   /home/<USER>/test_ws/dual_ur5/src/dual_arm_model/package.xml
[1.137s]   /home/<USER>/test_ws/build/dual_arm_model/ament_cmake_core/package.cmake)
[1.137s]   returned error code 1
[1.137s] Call Stack (most recent call first):
[1.138s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
[1.138s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)
[1.138s]   CMakeLists.txt:29 (ament_lint_auto_find_test_dependencies)
[1.138s] 
[1.138s] [0m
[1.138s] -- Configuring incomplete, errors occurred!
[1.138s] See also "/home/<USER>/test_ws/build/dual_arm_model/CMakeFiles/CMakeOutput.log".

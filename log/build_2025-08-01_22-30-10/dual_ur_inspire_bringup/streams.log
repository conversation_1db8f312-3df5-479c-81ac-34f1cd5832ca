[0.021s] Invoking command in '/home/<USER>/test_ws/build/dual_ur_inspire_bringup': CMAKE_PREFIX_PATH=/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake /home/<USER>/test_ws/dual_ur5/src/dual_ur_inspire_bringup -DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/dual_ur_inspire_bringup
[0.361s] -- The C compiler identification is GNU 12.3.0
[0.361s] -- The CXX compiler identification is GNU 12.3.0
[0.361s] -- Detecting C compiler ABI info
[0.361s] -- Detecting C compiler ABI info - done
[0.361s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.361s] -- Detecting C compile features
[0.361s] -- Detecting C compile features - done
[0.361s] -- Detecting CXX compiler ABI info
[0.361s] -- Detecting CXX compiler <PERSON><PERSON> info - done
[0.362s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.362s] -- Detecting CXX compile features
[0.362s] -- Detecting CXX compile features - done
[0.362s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.642s] -- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter 
[0.728s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.750s] Traceback (most recent call last):
[0.750s]   File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>
[0.750s]     from catkin_pkg.package import parse_package_string
[0.750s] ModuleNotFoundError: No module named 'catkin_pkg'
[0.752s] [31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):
[0.752s]   execute_process(/home/<USER>/anaconda3/bin/python3
[0.752s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py
[0.752s]   /home/<USER>/test_ws/dual_ur5/src/dual_ur_inspire_bringup/package.xml
[0.752s]   /home/<USER>/test_ws/build/dual_ur_inspire_bringup/ament_cmake_core/package.cmake)
[0.752s]   returned error code 1
[0.752s] Call Stack (most recent call first):
[0.753s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
[0.753s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)
[0.753s]   CMakeLists.txt:32 (ament_lint_auto_find_test_dependencies)
[0.753s] 
[0.753s] [0m
[0.753s] -- Configuring incomplete, errors occurred!
[0.753s] See also "/home/<USER>/test_ws/build/dual_ur_inspire_bringup/CMakeFiles/CMakeOutput.log".
[0.756s] Invoked command in '/home/<USER>/test_ws/build/dual_ur_inspire_bringup' returned '1': CMAKE_PREFIX_PATH=/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake /home/<USER>/test_ws/dual_ur5/src/dual_ur_inspire_bringup -DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/dual_ur_inspire_bringup

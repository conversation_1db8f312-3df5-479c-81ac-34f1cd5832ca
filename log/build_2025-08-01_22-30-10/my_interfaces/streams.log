[0.022s] Invoking command in '/home/<USER>/test_ws/build/my_interfaces': CMAKE_PREFIX_PATH=/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake /home/<USER>/test_ws/dual_ur5/src/Paxini_Hand/my_interfaces -DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/my_interfaces
[0.363s] -- The C compiler identification is GNU 12.3.0
[0.363s] -- The CXX compiler identification is GNU 12.3.0
[0.363s] -- Detecting C compiler ABI info
[0.363s] -- Detecting C compiler ABI info - done
[0.363s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.364s] -- Detecting C compile features
[0.364s] -- Detecting C compile features - done
[0.364s] -- Detecting CXX compiler ABI info
[0.364s] -- Detecting CXX compiler ABI info - done
[0.364s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.364s] -- Detecting CXX compile features
[0.364s] -- Detecting CXX compile features - done
[0.364s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.682s] -- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter 
[0.762s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.765s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.770s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.808s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.906s] [31mCMake Error at /opt/ros/humble/share/rosidl_adapter/cmake/rosidl_adapt_interfaces.cmake:59 (message):
[0.906s]   execute_process(/home/<USER>/anaconda3/bin/python3 -m rosidl_adapter
[0.906s]   --package-name my_interfaces --arguments-file
[0.906s]   /home/<USER>/test_ws/build/my_interfaces/rosidl_adapter__arguments__my_interfaces.json
[0.906s]   --output-dir
[0.906s]   /home/<USER>/test_ws/build/my_interfaces/rosidl_adapter/my_interfaces
[0.907s]   --output-file
[0.907s]   /home/<USER>/test_ws/build/my_interfaces/rosidl_adapter/my_interfaces.idls)
[0.907s]   returned error code 1:
[0.907s] 
[0.907s]   Traceback (most recent call last):
[0.907s] 
[0.907s]     File "<frozen runpy>", line 198, in _run_module_as_main
[0.907s]     File "<frozen runpy>", line 88, in _run_code
[0.907s]     File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/__main__.py", line 19, in <module>
[0.907s]       sys.exit(main())
[0.907s]                ^^^^^^
[0.907s]     File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/main.py", line 53, in main
[0.907s]       abs_idl_file = convert_to_idl(
[0.907s]                      ^^^^^^^^^^^^^^^
[0.907s]     File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/__init__.py", line 23, in convert_to_idl
[0.907s]       from rosidl_adapter.srv import convert_srv_to_idl
[0.907s]     File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/srv/__init__.py", line 16, in <module>
[0.908s]       from rosidl_adapter.resource import expand_template
[0.908s]     File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/resource/__init__.py", line 19, in <module>
[0.908s]       import em
[0.908s] 
[0.908s]   ModuleNotFoundError: No module named 'em'
[0.908s] 
[0.908s] Call Stack (most recent call first):
[0.908s]   /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:130 (rosidl_adapt_interfaces)
[0.908s]   CMakeLists.txt:14 (rosidl_generate_interfaces)
[0.908s] 
[0.908s] [0m
[0.908s] -- Configuring incomplete, errors occurred!
[0.908s] See also "/home/<USER>/test_ws/build/my_interfaces/CMakeFiles/CMakeOutput.log".

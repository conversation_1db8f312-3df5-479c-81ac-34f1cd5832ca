-- The C compiler identification is GNU 12.3.0
-- The CXX compiler identification is GNU 12.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter 
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[31mCMake Error at /opt/ros/humble/share/rosidl_adapter/cmake/rosidl_adapt_interfaces.cmake:59 (message):
  execute_process(/home/<USER>/anaconda3/bin/python3 -m rosidl_adapter
  --package-name my_interfaces --arguments-file
  /home/<USER>/test_ws/build/my_interfaces/rosidl_adapter__arguments__my_interfaces.json
  --output-dir
  /home/<USER>/test_ws/build/my_interfaces/rosidl_adapter/my_interfaces
  --output-file
  /home/<USER>/test_ws/build/my_interfaces/rosidl_adapter/my_interfaces.idls)
  returned error code 1:

  Traceback (most recent call last):

    File "<frozen runpy>", line 198, in _run_module_as_main
    File "<frozen runpy>", line 88, in _run_code
    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/__main__.py", line 19, in <module>
      sys.exit(main())
               ^^^^^^
    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/main.py", line 53, in main
      abs_idl_file = convert_to_idl(
                     ^^^^^^^^^^^^^^^
    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/__init__.py", line 23, in convert_to_idl
      from rosidl_adapter.srv import convert_srv_to_idl
    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/srv/__init__.py", line 16, in <module>
      from rosidl_adapter.resource import expand_template
    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/resource/__init__.py", line 19, in <module>
      import em

  ModuleNotFoundError: No module named 'em'

Call Stack (most recent call first):
  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:130 (rosidl_adapt_interfaces)
  CMakeLists.txt:14 (rosidl_generate_interfaces)

[0m
-- Configuring incomplete, errors occurred!
See also "/home/<USER>/test_ws/build/my_interfaces/CMakeFiles/CMakeOutput.log".

[0.000000] (-) TimerEvent: {}
[0.000260] (dual_arm_model) JobQueued: {'identifier': 'dual_arm_model', 'dependencies': OrderedDict()}
[0.000291] (dual_ur_inspire_bringup) JobQueued: {'identifier': 'dual_ur_inspire_bringup', 'dependencies': OrderedDict()}
[0.000564] (inspire_hand_interfaces) JobQueued: {'identifier': 'inspire_hand_interfaces', 'dependencies': OrderedDict()}
[0.000675] (joint_state_filter) JobQueued: {'identifier': 'joint_state_filter', 'dependencies': OrderedDict()}
[0.000710] (my_interfaces) JobQueued: {'identifier': 'my_interfaces', 'dependencies': OrderedDict()}
[0.000736] (py_srvcli) JobQueued: {'identifier': 'py_srvcli', 'dependencies': OrderedDict()}
[0.000780] (rtde_pkg) JobQueued: {'identifier': 'rtde_pkg', 'dependencies': OrderedDict()}
[0.000801] (trac_ik_lib) JobQueued: {'identifier': 'trac_ik_lib', 'dependencies': OrderedDict()}
[0.000822] (ur_description) JobQueued: {'identifier': 'ur_description', 'dependencies': OrderedDict()}
[0.000839] (vr_camera_node) JobQueued: {'identifier': 'vr_camera_node', 'dependencies': OrderedDict()}
[0.000888] (arm_node) JobQueued: {'identifier': 'arm_node', 'dependencies': OrderedDict([('trac_ik_lib', '/home/<USER>/test_ws/install/trac_ik_lib')])}
[0.000908] (dexh13_communication) JobQueued: {'identifier': 'dexh13_communication', 'dependencies': OrderedDict([('my_interfaces', '/home/<USER>/test_ws/install/my_interfaces')])}
[0.000927] (inspire_hand_demo) JobQueued: {'identifier': 'inspire_hand_demo', 'dependencies': OrderedDict([('inspire_hand_interfaces', '/home/<USER>/test_ws/install/inspire_hand_interfaces')])}
[0.000946] (trac_ik_examples) JobQueued: {'identifier': 'trac_ik_examples', 'dependencies': OrderedDict([('trac_ik_lib', '/home/<USER>/test_ws/install/trac_ik_lib')])}
[0.000966] (trac_ik) JobQueued: {'identifier': 'trac_ik', 'dependencies': OrderedDict([('trac_ik_lib', '/home/<USER>/test_ws/install/trac_ik_lib'), ('trac_ik_examples', '/home/<USER>/test_ws/install/trac_ik_examples')])}
[0.000985] (trac_ik_lib) JobStarted: {'identifier': 'trac_ik_lib'}
[0.003545] (inspire_hand_interfaces) JobStarted: {'identifier': 'inspire_hand_interfaces'}
[0.005292] (my_interfaces) JobStarted: {'identifier': 'my_interfaces'}
[0.007675] (dual_arm_model) JobStarted: {'identifier': 'dual_arm_model'}
[0.009233] (dual_ur_inspire_bringup) JobStarted: {'identifier': 'dual_ur_inspire_bringup'}
[0.011054] (joint_state_filter) JobStarted: {'identifier': 'joint_state_filter'}
[0.012561] (py_srvcli) JobStarted: {'identifier': 'py_srvcli'}
[0.015542] (rtde_pkg) JobStarted: {'identifier': 'rtde_pkg'}
[0.018306] (ur_description) JobStarted: {'identifier': 'ur_description'}
[0.019946] (vr_camera_node) JobStarted: {'identifier': 'vr_camera_node'}
[0.023864] (trac_ik_lib) JobProgress: {'identifier': 'trac_ik_lib', 'progress': 'cmake'}
[0.024079] (trac_ik_lib) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/test_ws/dual_ur5/src/trac_ik/trac_ik_lib', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/trac_ik_lib'], 'cwd': '/home/<USER>/test_ws/build/trac_ik_lib', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('CONDA_PROMPT_MODIFIER', '(base)'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'lwy'), ('LC_TIME', 'zh_CN.UTF-8'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '1'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('OLDPWD', '/home/<USER>/test_ws'), ('TERM_PROGRAM_VERSION', '1.102.2'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/201/usr/share/code/code'), ('MANAGERPID', '1198'), ('SYSTEMD_EXEC_PID', '1578'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '54580'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'lwy'), ('JOURNAL_STREAM', '8:9396'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'lwy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools'), ('SESSION_MANAGER', 'local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555'), ('GTK_EXE_PREFIX', '/snap/code/201/usr'), ('INVOCATION_ID', '0d599cd8298c4bbe9f8e5cb2c79c41da'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/201/usr/lib/locale'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/201/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1be5817c21.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/anaconda3/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('CONDA_DEFAULT_ENV', 'base'), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/test_ws/build/trac_ik_lib'), ('CUDA_HOME', '/usr/local/cuda-12.1'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('CONDA_PREFIX', '/home/<USER>/anaconda3'), ('GSETTINGS_SCHEMA_DIR_CONDA_BACKUP', '/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.025768] (inspire_hand_interfaces) JobProgress: {'identifier': 'inspire_hand_interfaces', 'progress': 'cmake'}
[0.026039] (inspire_hand_interfaces) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/test_ws/dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/inspire_hand_interfaces'], 'cwd': '/home/<USER>/test_ws/build/inspire_hand_interfaces', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('CONDA_PROMPT_MODIFIER', '(base)'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'lwy'), ('LC_TIME', 'zh_CN.UTF-8'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '1'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('OLDPWD', '/home/<USER>/test_ws'), ('TERM_PROGRAM_VERSION', '1.102.2'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/201/usr/share/code/code'), ('MANAGERPID', '1198'), ('SYSTEMD_EXEC_PID', '1578'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '54580'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'lwy'), ('JOURNAL_STREAM', '8:9396'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'lwy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools'), ('SESSION_MANAGER', 'local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555'), ('GTK_EXE_PREFIX', '/snap/code/201/usr'), ('INVOCATION_ID', '0d599cd8298c4bbe9f8e5cb2c79c41da'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/201/usr/lib/locale'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/201/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1be5817c21.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/anaconda3/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('CONDA_DEFAULT_ENV', 'base'), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/test_ws/build/inspire_hand_interfaces'), ('CUDA_HOME', '/usr/local/cuda-12.1'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('CONDA_PREFIX', '/home/<USER>/anaconda3'), ('GSETTINGS_SCHEMA_DIR_CONDA_BACKUP', '/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.026988] (my_interfaces) JobProgress: {'identifier': 'my_interfaces', 'progress': 'cmake'}
[0.027136] (my_interfaces) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/test_ws/dual_ur5/src/Paxini_Hand/my_interfaces', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/my_interfaces'], 'cwd': '/home/<USER>/test_ws/build/my_interfaces', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('CONDA_PROMPT_MODIFIER', '(base)'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'lwy'), ('LC_TIME', 'zh_CN.UTF-8'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '1'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('OLDPWD', '/home/<USER>/test_ws'), ('TERM_PROGRAM_VERSION', '1.102.2'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/201/usr/share/code/code'), ('MANAGERPID', '1198'), ('SYSTEMD_EXEC_PID', '1578'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '54580'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'lwy'), ('JOURNAL_STREAM', '8:9396'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'lwy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools'), ('SESSION_MANAGER', 'local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555'), ('GTK_EXE_PREFIX', '/snap/code/201/usr'), ('INVOCATION_ID', '0d599cd8298c4bbe9f8e5cb2c79c41da'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/201/usr/lib/locale'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/201/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1be5817c21.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/anaconda3/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('CONDA_DEFAULT_ENV', 'base'), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/test_ws/build/my_interfaces'), ('CUDA_HOME', '/usr/local/cuda-12.1'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('CONDA_PREFIX', '/home/<USER>/anaconda3'), ('GSETTINGS_SCHEMA_DIR_CONDA_BACKUP', '/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.028042] (dual_arm_model) JobProgress: {'identifier': 'dual_arm_model', 'progress': 'cmake'}
[0.028283] (dual_arm_model) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/test_ws/dual_ur5/src/dual_arm_model', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/dual_arm_model'], 'cwd': '/home/<USER>/test_ws/build/dual_arm_model', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('CONDA_PROMPT_MODIFIER', '(base)'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'lwy'), ('LC_TIME', 'zh_CN.UTF-8'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '1'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('OLDPWD', '/home/<USER>/test_ws'), ('TERM_PROGRAM_VERSION', '1.102.2'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/201/usr/share/code/code'), ('MANAGERPID', '1198'), ('SYSTEMD_EXEC_PID', '1578'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '54580'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'lwy'), ('JOURNAL_STREAM', '8:9396'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'lwy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools'), ('SESSION_MANAGER', 'local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555'), ('GTK_EXE_PREFIX', '/snap/code/201/usr'), ('INVOCATION_ID', '0d599cd8298c4bbe9f8e5cb2c79c41da'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/201/usr/lib/locale'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/201/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1be5817c21.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/anaconda3/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('CONDA_DEFAULT_ENV', 'base'), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/test_ws/build/dual_arm_model'), ('CUDA_HOME', '/usr/local/cuda-12.1'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('CONDA_PREFIX', '/home/<USER>/anaconda3'), ('GSETTINGS_SCHEMA_DIR_CONDA_BACKUP', '/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.029338] (dual_ur_inspire_bringup) JobProgress: {'identifier': 'dual_ur_inspire_bringup', 'progress': 'cmake'}
[0.029840] (dual_ur_inspire_bringup) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/test_ws/dual_ur5/src/dual_ur_inspire_bringup', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/dual_ur_inspire_bringup'], 'cwd': '/home/<USER>/test_ws/build/dual_ur_inspire_bringup', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('CONDA_PROMPT_MODIFIER', '(base)'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'lwy'), ('LC_TIME', 'zh_CN.UTF-8'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '1'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('OLDPWD', '/home/<USER>/test_ws'), ('TERM_PROGRAM_VERSION', '1.102.2'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/201/usr/share/code/code'), ('MANAGERPID', '1198'), ('SYSTEMD_EXEC_PID', '1578'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '54580'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'lwy'), ('JOURNAL_STREAM', '8:9396'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'lwy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools'), ('SESSION_MANAGER', 'local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555'), ('GTK_EXE_PREFIX', '/snap/code/201/usr'), ('INVOCATION_ID', '0d599cd8298c4bbe9f8e5cb2c79c41da'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/201/usr/lib/locale'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/201/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1be5817c21.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/anaconda3/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('CONDA_DEFAULT_ENV', 'base'), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/test_ws/build/dual_ur_inspire_bringup'), ('CUDA_HOME', '/usr/local/cuda-12.1'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('CONDA_PREFIX', '/home/<USER>/anaconda3'), ('GSETTINGS_SCHEMA_DIR_CONDA_BACKUP', '/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.030909] (joint_state_filter) JobProgress: {'identifier': 'joint_state_filter', 'progress': 'cmake'}
[0.031320] (joint_state_filter) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/test_ws/dual_ur5/src/joint_state_filter', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/joint_state_filter'], 'cwd': '/home/<USER>/test_ws/build/joint_state_filter', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('CONDA_PROMPT_MODIFIER', '(base)'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'lwy'), ('LC_TIME', 'zh_CN.UTF-8'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '1'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('OLDPWD', '/home/<USER>/test_ws'), ('TERM_PROGRAM_VERSION', '1.102.2'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/201/usr/share/code/code'), ('MANAGERPID', '1198'), ('SYSTEMD_EXEC_PID', '1578'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '54580'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'lwy'), ('JOURNAL_STREAM', '8:9396'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'lwy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools'), ('SESSION_MANAGER', 'local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555'), ('GTK_EXE_PREFIX', '/snap/code/201/usr'), ('INVOCATION_ID', '0d599cd8298c4bbe9f8e5cb2c79c41da'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/201/usr/lib/locale'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/201/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1be5817c21.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/anaconda3/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('CONDA_DEFAULT_ENV', 'base'), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/test_ws/build/joint_state_filter'), ('CUDA_HOME', '/usr/local/cuda-12.1'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('CONDA_PREFIX', '/home/<USER>/anaconda3'), ('GSETTINGS_SCHEMA_DIR_CONDA_BACKUP', '/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.099230] (-) TimerEvent: {}
[0.199561] (-) TimerEvent: {}
[0.299913] (-) TimerEvent: {}
[0.364025] (ur_description) JobProgress: {'identifier': 'ur_description', 'progress': 'cmake'}
[0.365010] (ur_description) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/test_ws/dual_ur5/src/Universal_Robots_ROS2_Description', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/ur_description'], 'cwd': '/home/<USER>/test_ws/build/ur_description', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('CONDA_PROMPT_MODIFIER', '(base)'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'lwy'), ('LC_TIME', 'zh_CN.UTF-8'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '1'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('OLDPWD', '/home/<USER>/test_ws'), ('TERM_PROGRAM_VERSION', '1.102.2'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/201/usr/share/code/code'), ('MANAGERPID', '1198'), ('SYSTEMD_EXEC_PID', '1578'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '54580'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'lwy'), ('JOURNAL_STREAM', '8:9396'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'lwy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools'), ('SESSION_MANAGER', 'local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555'), ('GTK_EXE_PREFIX', '/snap/code/201/usr'), ('INVOCATION_ID', '0d599cd8298c4bbe9f8e5cb2c79c41da'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/201/usr/lib/locale'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/201/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1be5817c21.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/anaconda3/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('CONDA_DEFAULT_ENV', 'base'), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/test_ws/build/ur_description'), ('CUDA_HOME', '/usr/local/cuda-12.1'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('CONDA_PREFIX', '/home/<USER>/anaconda3'), ('GSETTINGS_SCHEMA_DIR_CONDA_BACKUP', '/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.366869] (trac_ik_lib) StdoutLine: {'line': b'-- The C compiler identification is GNU 12.3.0\n'}
[0.367407] (trac_ik_lib) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 12.3.0\n'}
[0.367476] (trac_ik_lib) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.367526] (trac_ik_lib) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.367575] (trac_ik_lib) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.367622] (trac_ik_lib) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.367668] (trac_ik_lib) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.367711] (trac_ik_lib) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.367756] (trac_ik_lib) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.367800] (trac_ik_lib) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.367846] (trac_ik_lib) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.367891] (trac_ik_lib) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.367937] (trac_ik_lib) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.367982] (inspire_hand_interfaces) StdoutLine: {'line': b'-- The C compiler identification is GNU 12.3.0\n'}
[0.368254] (inspire_hand_interfaces) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 12.3.0\n'}
[0.368313] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.368362] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.368411] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.368460] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.368505] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.368550] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.368593] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.368638] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.368682] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.368719] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.368755] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.368796] (my_interfaces) StdoutLine: {'line': b'-- The C compiler identification is GNU 12.3.0\n'}
[0.368998] (my_interfaces) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 12.3.0\n'}
[0.369033] (my_interfaces) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.369062] (my_interfaces) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.369125] (my_interfaces) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.369154] (my_interfaces) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.369182] (my_interfaces) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.369209] (my_interfaces) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.369241] (my_interfaces) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.369272] (my_interfaces) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.369300] (my_interfaces) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.369327] (my_interfaces) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.369353] (my_interfaces) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.369380] (dual_arm_model) StdoutLine: {'line': b'-- The C compiler identification is GNU 12.3.0\n'}
[0.369580] (dual_arm_model) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 12.3.0\n'}
[0.369610] (dual_arm_model) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.369639] (dual_arm_model) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.369667] (dual_arm_model) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.369694] (dual_arm_model) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.369721] (dual_arm_model) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.369748] (dual_arm_model) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.369775] (dual_arm_model) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.369802] (dual_arm_model) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.369829] (dual_arm_model) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.369856] (dual_arm_model) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.369884] (dual_arm_model) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.370263] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- The C compiler identification is GNU 12.3.0\n'}
[0.370545] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 12.3.0\n'}
[0.370578] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.370606] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.370633] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.370661] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.370689] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.370716] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.370743] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.370771] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.370798] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.370826] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.370852] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.370880] (joint_state_filter) StdoutLine: {'line': b'-- The C compiler identification is GNU 12.3.0\n'}
[0.371032] (joint_state_filter) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 12.3.0\n'}
[0.371064] (joint_state_filter) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.371093] (joint_state_filter) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.371122] (joint_state_filter) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.371183] (joint_state_filter) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.371213] (joint_state_filter) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.371245] (joint_state_filter) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.371277] (joint_state_filter) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.371304] (joint_state_filter) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.371332] (joint_state_filter) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.371358] (joint_state_filter) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.371385] (joint_state_filter) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.400023] (-) TimerEvent: {}
[0.410013] (ur_description) StdoutLine: {'line': b'-- The C compiler identification is GNU 12.3.0\n'}
[0.456457] (ur_description) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 12.3.0\n'}
[0.463177] (ur_description) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.500127] (-) TimerEvent: {}
[0.515664] (ur_description) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.520402] (ur_description) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.520619] (ur_description) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.520784] (ur_description) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.522892] (ur_description) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.586318] (ur_description) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.590954] (ur_description) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.591181] (ur_description) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.591418] (ur_description) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.592661] (ur_description) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.600307] (-) TimerEvent: {}
[0.645288] (joint_state_filter) StdoutLine: {'line': b'-- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter \n'}
[0.650960] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter \n'}
[0.655558] (dual_arm_model) StdoutLine: {'line': b'-- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter \n'}
[0.669600] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter \n'}
[0.681541] (trac_ik_lib) StdoutLine: {'line': b'-- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter \n'}
[0.687989] (my_interfaces) StdoutLine: {'line': b'-- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter \n'}
[0.700398] (-) TimerEvent: {}
[0.737594] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.738124] (dual_arm_model) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.741374] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.744858] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.748114] (joint_state_filter) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[0.752200] (trac_ik_lib) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[0.752394] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.758757] (dual_ur_inspire_bringup) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[0.758960] (dual_ur_inspire_bringup) StderrLine: {'line': b'  File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>\n'}
[0.759015] (dual_ur_inspire_bringup) StderrLine: {'line': b'    from catkin_pkg.package import parse_package_string\n'}
[0.759047] (dual_ur_inspire_bringup) StderrLine: {'line': b"ModuleNotFoundError: No module named 'catkin_pkg'\n"}
[0.761432] (dual_ur_inspire_bringup) StderrLine: {'line': b'\x1b[31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):\n'}
[0.761577] (dual_ur_inspire_bringup) StderrLine: {'line': b'  execute_process(/home/<USER>/anaconda3/bin/python3\n'}
[0.761622] (dual_ur_inspire_bringup) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py\n'}
[0.761654] (dual_ur_inspire_bringup) StderrLine: {'line': b'  /home/<USER>/test_ws/dual_ur5/src/dual_ur_inspire_bringup/package.xml\n'}
[0.761683] (dual_ur_inspire_bringup) StderrLine: {'line': b'  /home/<USER>/test_ws/build/dual_ur_inspire_bringup/ament_cmake_core/package.cmake)\n'}
[0.761714] (dual_ur_inspire_bringup) StderrLine: {'line': b'  returned error code 1\n'}
[0.761750] (dual_ur_inspire_bringup) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[0.761779] (dual_ur_inspire_bringup) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)\n'}
[0.761807] (dual_ur_inspire_bringup) StderrLine: {'line': b'  /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)\n'}
[0.761836] (dual_ur_inspire_bringup) StderrLine: {'line': b'  CMakeLists.txt:32 (ament_lint_auto_find_test_dependencies)\n'}
[0.761864] (dual_ur_inspire_bringup) StderrLine: {'line': b'\n'}
[0.761891] (dual_ur_inspire_bringup) StderrLine: {'line': b'\x1b[0m\n'}
[0.761955] (dual_ur_inspire_bringup) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[0.761993] (dual_ur_inspire_bringup) StdoutLine: {'line': b'See also "/home/<USER>/test_ws/build/dual_ur_inspire_bringup/CMakeFiles/CMakeOutput.log".\n'}
[0.764026] (trac_ik_lib) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0") \n'}
[0.764921] (dual_ur_inspire_bringup) CommandEnded: {'returncode': 1}
[0.765682] (trac_ik_lib) StdoutLine: {'line': b'-- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)\n'}
[0.767730] (my_interfaces) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.770228] (my_interfaces) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.775653] (my_interfaces) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.777190] (trac_ik_lib) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[0.778929] (joint_state_filter) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.781700] (dual_arm_model) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.782220] (dual_ur_inspire_bringup) JobEnded: {'identifier': 'dual_ur_inspire_bringup', 'rc': 1}
[0.783278] (joint_state_filter) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.786033] (dual_arm_model) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.786094] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.789600] (trac_ik_lib) StderrLine: {'line': b'\x1b[31mCMake Error at CMakeLists.txt:22 (find_package):\n'}
[0.789779] (trac_ik_lib) StderrLine: {'line': b'  By not providing "FindNLopt.cmake" in CMAKE_MODULE_PATH this project has\n'}
[0.789824] (trac_ik_lib) StderrLine: {'line': b'  asked CMake to find a package configuration file provided by "NLopt", but\n'}
[0.789856] (trac_ik_lib) StderrLine: {'line': b'  CMake did not find one.\n'}
[0.789886] (trac_ik_lib) StderrLine: {'line': b'\n'}
[0.789913] (trac_ik_lib) StderrLine: {'line': b'  Could not find a package configuration file provided by "NLopt" with any of\n'}
[0.789941] (trac_ik_lib) StderrLine: {'line': b'  the following names:\n'}
[0.789966] (trac_ik_lib) StderrLine: {'line': b'\n'}
[0.789992] (trac_ik_lib) StderrLine: {'line': b'    NLoptConfig.cmake\n'}
[0.790017] (trac_ik_lib) StderrLine: {'line': b'    nlopt-config.cmake\n'}
[0.790043] (trac_ik_lib) StderrLine: {'line': b'\n'}
[0.790067] (trac_ik_lib) StderrLine: {'line': b'  Add the installation prefix of "NLopt" to CMAKE_PREFIX_PATH or set\n'}
[0.790093] (trac_ik_lib) StderrLine: {'line': b'  "NLopt_DIR" to a directory containing one of the above files.  If "NLopt"\n'}
[0.790117] (trac_ik_lib) StderrLine: {'line': b'  provides a separate development package or SDK, be sure it has been\n'}
[0.790143] (trac_ik_lib) StderrLine: {'line': b'  installed.\n'}
[0.790167] (trac_ik_lib) StderrLine: {'line': b'\n'}
[0.790193] (trac_ik_lib) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[0.790224] (trac_ik_lib) StdoutLine: {'line': b'See also "/home/<USER>/test_ws/build/trac_ik_lib/CMakeFiles/CMakeOutput.log".\n'}
[0.790263] (trac_ik_lib) StderrLine: {'line': b'\x1b[0m\n'}
[0.791757] (joint_state_filter) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.792251] (dual_arm_model) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.793490] (trac_ik_lib) JobEnded: {'identifier': 'trac_ik_lib', 'rc': 'SIGINT'}
[0.800490] (-) TimerEvent: {}
[0.800909] (dual_arm_model) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.801064] (joint_state_filter) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.810241] (joint_state_filter) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.810424] (dual_arm_model) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.813758] (my_interfaces) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.814489] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.819429] (vr_camera_node) JobEnded: {'identifier': 'vr_camera_node', 'rc': 'SIGINT'}
[0.826793] (py_srvcli) JobEnded: {'identifier': 'py_srvcli', 'rc': 'SIGINT'}
[0.836751] (inspire_hand_interfaces) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[0.836945] (inspire_hand_interfaces) StderrLine: {'line': b'  File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>\n'}
[0.837018] (inspire_hand_interfaces) StderrLine: {'line': b'    from catkin_pkg.package import parse_package_string\n'}
[0.837082] (inspire_hand_interfaces) StderrLine: {'line': b"ModuleNotFoundError: No module named 'catkin_pkg'\n"}
[0.839177] (inspire_hand_interfaces) StderrLine: {'line': b'\x1b[31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):\n'}
[0.839320] (inspire_hand_interfaces) StderrLine: {'line': b'  execute_process(/home/<USER>/anaconda3/bin/python3\n'}
[0.839504] (inspire_hand_interfaces) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py\n'}
[0.839570] (inspire_hand_interfaces) StderrLine: {'line': b'  /home/<USER>/test_ws/dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces/package.xml\n'}
[0.839630] (inspire_hand_interfaces) StderrLine: {'line': b'  /home/<USER>/test_ws/build/inspire_hand_interfaces/ament_cmake_core/package.cmake)\n'}
[0.839688] (inspire_hand_interfaces) StderrLine: {'line': b'  returned error code 1\n'}
[0.839746] (inspire_hand_interfaces) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[0.839804] (inspire_hand_interfaces) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)\n'}
[0.839862] (inspire_hand_interfaces) StderrLine: {'line': b'  /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)\n'}
[0.839919] (inspire_hand_interfaces) StderrLine: {'line': b'  CMakeLists.txt:24 (ament_lint_auto_find_test_dependencies)\n'}
[0.839975] (inspire_hand_interfaces) StderrLine: {'line': b'\n'}
[0.840031] (inspire_hand_interfaces) StderrLine: {'line': b'\x1b[0m\n'}
[0.840086] (inspire_hand_interfaces) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[0.840153] (inspire_hand_interfaces) StdoutLine: {'line': b'See also "/home/<USER>/test_ws/build/inspire_hand_interfaces/CMakeFiles/CMakeOutput.log".\n'}
[0.843285] (inspire_hand_interfaces) JobEnded: {'identifier': 'inspire_hand_interfaces', 'rc': 'SIGINT'}
[0.852126] (rtde_pkg) JobEnded: {'identifier': 'rtde_pkg', 'rc': 'SIGINT'}
[0.853740] (dual_arm_model) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.855464] (dual_arm_model) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.869586] (joint_state_filter) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.890227] (joint_state_filter) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.891509] (joint_state_filter) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.899896] (ur_description) StdoutLine: {'line': b'-- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter \n'}
[0.900604] (-) TimerEvent: {}
[0.911643] (my_interfaces) StderrLine: {'line': b'\x1b[31mCMake Error at /opt/ros/humble/share/rosidl_adapter/cmake/rosidl_adapt_interfaces.cmake:59 (message):\n'}
[0.911856] (my_interfaces) StderrLine: {'line': b'  execute_process(/home/<USER>/anaconda3/bin/python3 -m rosidl_adapter\n'}
[0.911927] (my_interfaces) StderrLine: {'line': b'  --package-name my_interfaces --arguments-file\n'}
[0.911991] (my_interfaces) StderrLine: {'line': b'  /home/<USER>/test_ws/build/my_interfaces/rosidl_adapter__arguments__my_interfaces.json\n'}
[0.912053] (my_interfaces) StderrLine: {'line': b'  --output-dir\n'}
[0.912112] (my_interfaces) StderrLine: {'line': b'  /home/<USER>/test_ws/build/my_interfaces/rosidl_adapter/my_interfaces\n'}
[0.912173] (my_interfaces) StderrLine: {'line': b'  --output-file\n'}
[0.912232] (my_interfaces) StderrLine: {'line': b'  /home/<USER>/test_ws/build/my_interfaces/rosidl_adapter/my_interfaces.idls)\n'}
[0.912303] (my_interfaces) StderrLine: {'line': b'  returned error code 1:\n'}
[0.912362] (my_interfaces) StderrLine: {'line': b'\n'}
[0.912417] (my_interfaces) StderrLine: {'line': b'  Traceback (most recent call last):\n'}
[0.912473] (my_interfaces) StderrLine: {'line': b'\n'}
[0.912528] (my_interfaces) StderrLine: {'line': b'    File "<frozen runpy>", line 198, in _run_module_as_main\n'}
[0.912583] (my_interfaces) StderrLine: {'line': b'    File "<frozen runpy>", line 88, in _run_code\n'}
[0.912638] (my_interfaces) StderrLine: {'line': b'    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/__main__.py", line 19, in <module>\n'}
[0.912699] (my_interfaces) StderrLine: {'line': b'      sys.exit(main())\n'}
[0.912754] (my_interfaces) StderrLine: {'line': b'               ^^^^^^\n'}
[0.912808] (my_interfaces) StderrLine: {'line': b'    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/main.py", line 53, in main\n'}
[0.912863] (my_interfaces) StderrLine: {'line': b'      abs_idl_file = convert_to_idl(\n'}
[0.912918] (my_interfaces) StderrLine: {'line': b'                     ^^^^^^^^^^^^^^^\n'}
[0.912971] (my_interfaces) StderrLine: {'line': b'    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/__init__.py", line 23, in convert_to_idl\n'}
[0.913031] (my_interfaces) StderrLine: {'line': b'      from rosidl_adapter.srv import convert_srv_to_idl\n'}
[0.913092] (my_interfaces) StderrLine: {'line': b'    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/srv/__init__.py", line 16, in <module>\n'}
[0.913154] (my_interfaces) StderrLine: {'line': b'      from rosidl_adapter.resource import expand_template\n'}
[0.913212] (my_interfaces) StderrLine: {'line': b'    File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_adapter/resource/__init__.py", line 19, in <module>\n'}
[0.913284] (my_interfaces) StderrLine: {'line': b'      import em\n'}
[0.913343] (my_interfaces) StderrLine: {'line': b'\n'}
[0.913400] (my_interfaces) StderrLine: {'line': b"  ModuleNotFoundError: No module named 'em'\n"}
[0.913458] (my_interfaces) StderrLine: {'line': b'\n'}
[0.913514] (my_interfaces) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[0.913571] (my_interfaces) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:130 (rosidl_adapt_interfaces)\n'}
[0.913627] (my_interfaces) StderrLine: {'line': b'  CMakeLists.txt:14 (rosidl_generate_interfaces)\n'}
[0.913683] (my_interfaces) StderrLine: {'line': b'\n'}
[0.913740] (my_interfaces) StderrLine: {'line': b'\x1b[0m\n'}
[0.913796] (my_interfaces) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[0.913861] (my_interfaces) StdoutLine: {'line': b'See also "/home/<USER>/test_ws/build/my_interfaces/CMakeFiles/CMakeOutput.log".\n'}
[0.916754] (my_interfaces) JobEnded: {'identifier': 'my_interfaces', 'rc': 'SIGINT'}
[0.921139] (dual_arm_model) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.939661] (dual_arm_model) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[0.949832] (joint_state_filter) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.967836] (dual_arm_model) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.968018] (joint_state_filter) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[0.970644] (ur_description) StdoutLine: {'line': b'-- Found ament_cmake_pytest: 1.3.11 (/opt/ros/humble/share/ament_cmake_pytest/cmake)\n'}
[0.976367] (dual_arm_model) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[1.000744] (-) TimerEvent: {}
[1.001304] (joint_state_filter) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.006931] (joint_state_filter) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[1.031686] (dual_arm_model) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[1.032182] (dual_arm_model) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.063676] (joint_state_filter) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[1.063856] (joint_state_filter) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.086550] (dual_arm_model) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.087428] (dual_arm_model) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.100874] (-) TimerEvent: {}
[1.120646] (joint_state_filter) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.121503] (joint_state_filter) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.124484] (dual_arm_model) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.142190] (dual_arm_model) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[1.142374] (dual_arm_model) StderrLine: {'line': b'  File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>\n'}
[1.142443] (dual_arm_model) StderrLine: {'line': b'    from catkin_pkg.package import parse_package_string\n'}
[1.142504] (dual_arm_model) StderrLine: {'line': b"ModuleNotFoundError: No module named 'catkin_pkg'\n"}
[1.144344] (dual_arm_model) StderrLine: {'line': b'\x1b[31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):\n'}
[1.144474] (dual_arm_model) StderrLine: {'line': b'  execute_process(/home/<USER>/anaconda3/bin/python3\n'}
[1.144540] (dual_arm_model) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py\n'}
[1.144599] (dual_arm_model) StderrLine: {'line': b'  /home/<USER>/test_ws/dual_ur5/src/dual_arm_model/package.xml\n'}
[1.144658] (dual_arm_model) StderrLine: {'line': b'  /home/<USER>/test_ws/build/dual_arm_model/ament_cmake_core/package.cmake)\n'}
[1.144841] (dual_arm_model) StderrLine: {'line': b'  returned error code 1\n'}
[1.145091] (dual_arm_model) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.145171] (dual_arm_model) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)\n'}
[1.145230] (dual_arm_model) StderrLine: {'line': b'  /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)\n'}
[1.145296] (dual_arm_model) StderrLine: {'line': b'  CMakeLists.txt:29 (ament_lint_auto_find_test_dependencies)\n'}
[1.145352] (dual_arm_model) StderrLine: {'line': b'\n'}
[1.145405] (dual_arm_model) StderrLine: {'line': b'\x1b[0m\n'}
[1.145460] (dual_arm_model) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[1.145522] (dual_arm_model) StdoutLine: {'line': b'See also "/home/<USER>/test_ws/build/dual_arm_model/CMakeFiles/CMakeOutput.log".\n'}
[1.145579] (joint_state_filter) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.151890] (dual_arm_model) JobEnded: {'identifier': 'dual_arm_model', 'rc': 'SIGINT'}
[1.161114] (joint_state_filter) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[1.161281] (joint_state_filter) StderrLine: {'line': b'  File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>\n'}
[1.161350] (joint_state_filter) StderrLine: {'line': b'    from catkin_pkg.package import parse_package_string\n'}
[1.161409] (joint_state_filter) StderrLine: {'line': b"ModuleNotFoundError: No module named 'catkin_pkg'\n"}
[1.163409] (joint_state_filter) StderrLine: {'line': b'\x1b[31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):\n'}
[1.163552] (joint_state_filter) StderrLine: {'line': b'  execute_process(/home/<USER>/anaconda3/bin/python3\n'}
[1.163618] (joint_state_filter) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py\n'}
[1.163780] (joint_state_filter) StderrLine: {'line': b'  /home/<USER>/test_ws/dual_ur5/src/joint_state_filter/package.xml\n'}
[1.163845] (joint_state_filter) StderrLine: {'line': b'  /home/<USER>/test_ws/build/joint_state_filter/ament_cmake_core/package.cmake)\n'}
[1.163902] (joint_state_filter) StderrLine: {'line': b'  returned error code 1\n'}
[1.163958] (joint_state_filter) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.164012] (joint_state_filter) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)\n'}
[1.164068] (joint_state_filter) StderrLine: {'line': b'  /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)\n'}
[1.164130] (joint_state_filter) StderrLine: {'line': b'  CMakeLists.txt:36 (ament_lint_auto_find_test_dependencies)\n'}
[1.164184] (joint_state_filter) StderrLine: {'line': b'\n'}
[1.164242] (joint_state_filter) StderrLine: {'line': b'\x1b[0m\n'}
[1.164304] (joint_state_filter) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[1.164368] (joint_state_filter) StdoutLine: {'line': b'See also "/home/<USER>/test_ws/build/joint_state_filter/CMakeFiles/CMakeOutput.log".\n'}
[1.169938] (joint_state_filter) JobEnded: {'identifier': 'joint_state_filter', 'rc': 'SIGINT'}
[1.201105] (-) TimerEvent: {}
[1.207629] (ur_description) StderrLine: {'line': b'\x1b[33mCMake Warning at /opt/ros/humble/share/ament_cmake_pytest/cmake/ament_add_pytest_test.cmake:79 (message):\n'}
[1.207820] (ur_description) StderrLine: {'line': b"  The Python module 'pytest' was not found, pytests cannot be run.  On Linux,\n"}
[1.207886] (ur_description) StderrLine: {'line': b"  install the 'python3-pytest' package.  On other platforms, install 'pytest'\n"}
[1.207945] (ur_description) StderrLine: {'line': b'  using pip.\n'}
[1.208004] (ur_description) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.208059] (ur_description) StderrLine: {'line': b'  CMakeLists.txt:16 (ament_add_pytest_test)\n'}
[1.208114] (ur_description) StderrLine: {'line': b'\n'}
[1.208168] (ur_description) StderrLine: {'line': b'\x1b[0m\n'}
[1.301342] (-) TimerEvent: {}
[1.388523] (ur_description) StderrLine: {'line': b'\x1b[33mCMake Warning at /opt/ros/humble/share/ament_cmake_pytest/cmake/ament_add_pytest_test.cmake:79 (message):\n'}
[1.388681] (ur_description) StderrLine: {'line': b"  The Python module 'pytest' was not found, pytests cannot be run.  On Linux,\n"}
[1.388745] (ur_description) StderrLine: {'line': b"  install the 'python3-pytest' package.  On other platforms, install 'pytest'\n"}
[1.388804] (ur_description) StderrLine: {'line': b'  using pip.\n'}
[1.388859] (ur_description) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.388913] (ur_description) StderrLine: {'line': b'  CMakeLists.txt:17 (ament_add_pytest_test)\n'}
[1.388965] (ur_description) StderrLine: {'line': b'\n'}
[1.389018] (ur_description) StderrLine: {'line': b'\x1b[0m\n'}
[1.401422] (-) TimerEvent: {}
[1.405125] (ur_description) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[1.405277] (ur_description) StderrLine: {'line': b'  File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>\n'}
[1.405344] (ur_description) StderrLine: {'line': b'    from catkin_pkg.package import parse_package_string\n'}
[1.405401] (ur_description) StderrLine: {'line': b"ModuleNotFoundError: No module named 'catkin_pkg'\n"}
[1.407639] (ur_description) StderrLine: {'line': b'\x1b[31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):\n'}
[1.407883] (ur_description) StderrLine: {'line': b'  execute_process(/home/<USER>/anaconda3/bin/python3\n'}
[1.407950] (ur_description) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py\n'}
[1.408009] (ur_description) StderrLine: {'line': b'  /home/<USER>/test_ws/dual_ur5/src/Universal_Robots_ROS2_Description/package.xml\n'}
[1.408065] (ur_description) StderrLine: {'line': b'  /home/<USER>/test_ws/build/ur_description/ament_cmake_core/package.cmake)\n'}
[1.408120] (ur_description) StderrLine: {'line': b'  returned error code 1\n'}
[1.408173] (ur_description) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.408225] (ur_description) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)\n'}
[1.408287] (ur_description) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake:63 (ament_package_xml)\n'}
[1.408341] (ur_description) StderrLine: {'line': b'  CMakeLists.txt:20 (ament_package)\n'}
[1.408393] (ur_description) StderrLine: {'line': b'\n'}
[1.408452] (ur_description) StderrLine: {'line': b'\x1b[0m\n'}
[1.408504] (ur_description) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[1.408564] (ur_description) StdoutLine: {'line': b'See also "/home/<USER>/test_ws/build/ur_description/CMakeFiles/CMakeOutput.log".\n'}
[1.410786] (ur_description) JobEnded: {'identifier': 'ur_description', 'rc': 'SIGINT'}
[1.421405] (arm_node) JobSkipped: {'identifier': 'arm_node'}
[1.421449] (dexh13_communication) JobSkipped: {'identifier': 'dexh13_communication'}
[1.421469] (inspire_hand_demo) JobSkipped: {'identifier': 'inspire_hand_demo'}
[1.421487] (trac_ik_examples) JobSkipped: {'identifier': 'trac_ik_examples'}
[1.421503] (trac_ik) JobSkipped: {'identifier': 'trac_ik'}
[1.421518] (-) EventReactorShutdown: {}

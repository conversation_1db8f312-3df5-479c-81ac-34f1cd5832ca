[0.022s] Invoking command in '/home/<USER>/test_ws/build/joint_state_filter': CMAKE_PREFIX_PATH=/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake /home/<USER>/test_ws/dual_ur5/src/joint_state_filter -DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/joint_state_filter
[0.360s] -- The C compiler identification is GNU 12.3.0
[0.360s] -- The CXX compiler identification is GNU 12.3.0
[0.360s] -- Detecting C compiler ABI info
[0.360s] -- Detecting C compiler ABI info - done
[0.360s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.360s] -- Detecting C compile features
[0.360s] -- Detecting C compile features - done
[0.360s] -- Detecting CXX compiler ABI info
[0.360s] -- Detecting CXX compiler ABI info - done
[0.360s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.360s] -- Detecting CXX compile features
[0.360s] -- Detecting CXX compile features - done
[0.360s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.634s] -- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter 
[0.737s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.768s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.772s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.781s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.790s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.799s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.859s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.879s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.881s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.939s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.957s] -- Found FastRTPS: /opt/ros/humble/include  
[0.990s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.996s] -- Looking for pthread.h
[1.053s] -- Looking for pthread.h - found
[1.053s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.110s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.111s] -- Found Threads: TRUE  
[1.135s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.150s] Traceback (most recent call last):
[1.150s]   File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>
[1.150s]     from catkin_pkg.package import parse_package_string
[1.150s] ModuleNotFoundError: No module named 'catkin_pkg'
[1.152s] [31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):
[1.153s]   execute_process(/home/<USER>/anaconda3/bin/python3
[1.153s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py
[1.153s]   /home/<USER>/test_ws/dual_ur5/src/joint_state_filter/package.xml
[1.153s]   /home/<USER>/test_ws/build/joint_state_filter/ament_cmake_core/package.cmake)
[1.153s]   returned error code 1
[1.153s] Call Stack (most recent call first):
[1.153s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
[1.153s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)
[1.153s]   CMakeLists.txt:36 (ament_lint_auto_find_test_dependencies)
[1.153s] 
[1.153s] [0m
[1.153s] -- Configuring incomplete, errors occurred!
[1.153s] See also "/home/<USER>/test_ws/build/joint_state_filter/CMakeFiles/CMakeOutput.log".

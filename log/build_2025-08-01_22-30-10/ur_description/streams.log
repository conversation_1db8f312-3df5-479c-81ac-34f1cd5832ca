[0.348s] Invoking command in '/home/<USER>/test_ws/build/ur_description': CMAKE_PREFIX_PATH=/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake /home/<USER>/test_ws/dual_ur5/src/Universal_Robots_ROS2_Description -DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/ur_description
[0.392s] -- The C compiler identification is GNU 12.3.0
[0.438s] -- The CXX compiler identification is GNU 12.3.0
[0.445s] -- Detecting C compiler ABI info
[0.497s] -- Detecting C compiler ABI info - done
[0.502s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.502s] -- Detecting C compile features
[0.503s] -- Detecting C compile features - done
[0.505s] -- Detecting CXX compiler ABI info
[0.568s] -- Detecting CXX compiler ABI info - done
[0.573s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.573s] -- Detecting CXX compile features
[0.573s] -- Detecting CXX compile features - done
[0.574s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.882s] -- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter 
[0.952s] -- Found ament_cmake_pytest: 1.3.11 (/opt/ros/humble/share/ament_cmake_pytest/cmake)
[1.189s] [33mCMake Warning at /opt/ros/humble/share/ament_cmake_pytest/cmake/ament_add_pytest_test.cmake:79 (message):
[1.190s]   The Python module 'pytest' was not found, pytests cannot be run.  On Linux,
[1.190s]   install the 'python3-pytest' package.  On other platforms, install 'pytest'
[1.190s]   using pip.
[1.190s] Call Stack (most recent call first):
[1.190s]   CMakeLists.txt:16 (ament_add_pytest_test)
[1.190s] 
[1.190s] [0m
[1.370s] [33mCMake Warning at /opt/ros/humble/share/ament_cmake_pytest/cmake/ament_add_pytest_test.cmake:79 (message):
[1.370s]   The Python module 'pytest' was not found, pytests cannot be run.  On Linux,
[1.370s]   install the 'python3-pytest' package.  On other platforms, install 'pytest'
[1.371s]   using pip.
[1.371s] Call Stack (most recent call first):
[1.371s]   CMakeLists.txt:17 (ament_add_pytest_test)
[1.371s] 
[1.371s] [0m
[1.387s] Traceback (most recent call last):
[1.387s]   File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>
[1.387s]     from catkin_pkg.package import parse_package_string
[1.387s] ModuleNotFoundError: No module named 'catkin_pkg'
[1.390s] [31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):
[1.390s]   execute_process(/home/<USER>/anaconda3/bin/python3
[1.390s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py
[1.390s]   /home/<USER>/test_ws/dual_ur5/src/Universal_Robots_ROS2_Description/package.xml
[1.390s]   /home/<USER>/test_ws/build/ur_description/ament_cmake_core/package.cmake)
[1.390s]   returned error code 1
[1.390s] Call Stack (most recent call first):
[1.390s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
[1.390s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake:63 (ament_package_xml)
[1.390s]   CMakeLists.txt:20 (ament_package)
[1.390s] 
[1.390s] [0m
[1.390s] -- Configuring incomplete, errors occurred!
[1.390s] See also "/home/<USER>/test_ws/build/ur_description/CMakeFiles/CMakeOutput.log".

[33mCMake Warning at /opt/ros/humble/share/ament_cmake_pytest/cmake/ament_add_pytest_test.cmake:79 (message):
  The Python module 'pytest' was not found, pytests cannot be run.  On Linux,
  install the 'python3-pytest' package.  On other platforms, install 'pytest'
  using pip.
Call Stack (most recent call first):
  CMakeLists.txt:16 (ament_add_pytest_test)

[0m
[33mCMake Warning at /opt/ros/humble/share/ament_cmake_pytest/cmake/ament_add_pytest_test.cmake:79 (message):
  The Python module 'pytest' was not found, pytests cannot be run.  On Linux,
  install the 'python3-pytest' package.  On other platforms, install 'pytest'
  using pip.
Call Stack (most recent call first):
  CMakeLists.txt:17 (ament_add_pytest_test)

[0m
Traceback (most recent call last):
  File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>
    from catkin_pkg.package import parse_package_string
ModuleNotFoundError: No module named 'catkin_pkg'
[31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):
  execute_process(/home/<USER>/anaconda3/bin/python3
  /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py
  /home/<USER>/test_ws/dual_ur5/src/Universal_Robots_ROS2_Description/package.xml
  /home/<USER>/test_ws/build/ur_description/ament_cmake_core/package.cmake)
  returned error code 1
Call Stack (most recent call first):
  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake:63 (ament_package_xml)
  CMakeLists.txt:20 (ament_package)

[0m

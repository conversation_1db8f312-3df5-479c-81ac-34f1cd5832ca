[0.023s] Invoking command in '/home/<USER>/test_ws/build/inspire_hand_interfaces': CMAKE_PREFIX_PATH=/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake /home/<USER>/test_ws/dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces -DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/inspire_hand_interfaces
[0.364s] -- The C compiler identification is GNU 12.3.0
[0.364s] -- The CXX compiler identification is GNU 12.3.0
[0.364s] -- Detecting C compiler ABI info
[0.365s] -- Detecting C compiler ABI info - done
[0.365s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.365s] -- Detecting C compile features
[0.365s] -- Detecting C compile features - done
[0.365s] -- Detecting CXX compiler ABI info
[0.365s] -- Detecting CXX compiler ABI info - done
[0.365s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.365s] -- Detecting CXX compile features
[0.365s] -- Detecting CXX compile features - done
[0.365s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.666s] -- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter 
[0.738s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.741s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.749s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.782s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.811s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.833s] Traceback (most recent call last):
[0.833s]   File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 22, in <module>
[0.833s]     from catkin_pkg.package import parse_package_string
[0.833s] ModuleNotFoundError: No module named 'catkin_pkg'
[0.835s] [31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):
[0.836s]   execute_process(/home/<USER>/anaconda3/bin/python3
[0.836s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py
[0.836s]   /home/<USER>/test_ws/dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces/package.xml
[0.836s]   /home/<USER>/test_ws/build/inspire_hand_interfaces/ament_cmake_core/package.cmake)
[0.836s]   returned error code 1
[0.836s] Call Stack (most recent call first):
[0.836s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
[0.836s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)
[0.836s]   CMakeLists.txt:24 (ament_lint_auto_find_test_dependencies)
[0.836s] 
[0.836s] [0m
[0.836s] -- Configuring incomplete, errors occurred!
[0.836s] See also "/home/<USER>/test_ws/build/inspire_hand_interfaces/CMakeFiles/CMakeOutput.log".

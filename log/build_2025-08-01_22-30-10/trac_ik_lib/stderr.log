[31mCMake Error at CMakeLists.txt:22 (find_package):
  By not providing "FindNLopt.cmake" in CMAKE_MODULE_PATH this project has
  asked CMake to find a package configuration file provided by "NLopt", but
  CMake did not find one.

  Could not find a package configuration file provided by "NLopt" with any of
  the following names:

    NLoptConfig.cmake
    nlopt-config.cmake

  Add the installation prefix of "NLopt" to CMAKE_PREFIX_PATH or set
  "NLopt_DIR" to a directory containing one of the above files.  If "NLopt"
  provides a separate development package or SDK, be sure it has been
  installed.

[0m

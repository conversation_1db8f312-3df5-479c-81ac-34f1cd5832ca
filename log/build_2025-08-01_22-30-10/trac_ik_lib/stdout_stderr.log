-- The C compiler identification is GNU 12.3.0
-- The CXX compiler identification is GNU 12.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter 
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0") 
-- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[31mCMake Error at CMakeLists.txt:22 (find_package):
  By not providing "FindNLopt.cmake" in CMAKE_MODULE_PATH this project has
  asked CMake to find a package configuration file provided by "NLopt", but
  CMake did not find one.

  Could not find a package configuration file provided by "NLopt" with any of
  the following names:

    NLoptConfig.cmake
    nlopt-config.cmake

  Add the installation prefix of "NLopt" to CMAKE_PREFIX_PATH or set
  "NLopt_DIR" to a directory containing one of the above files.  If "NLopt"
  provides a separate development package or SDK, be sure it has been
  installed.

-- Configuring incomplete, errors occurred!
See also "/home/<USER>/test_ws/build/trac_ik_lib/CMakeFiles/CMakeOutput.log".
[0m

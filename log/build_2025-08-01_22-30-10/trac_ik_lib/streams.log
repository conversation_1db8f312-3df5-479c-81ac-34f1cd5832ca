[0.024s] Invoking command in '/home/<USER>/test_ws/build/trac_ik_lib': CMAKE_PREFIX_PATH=/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake /home/<USER>/test_ws/dual_ur5/src/trac_ik/trac_ik_lib -DCMAKE_INSTALL_PREFIX=/home/<USER>/test_ws/install/trac_ik_lib
[0.366s] -- The C compiler identification is GNU 12.3.0
[0.366s] -- The CXX compiler identification is GNU 12.3.0
[0.366s] -- Detecting C compiler ABI info
[0.367s] -- Detecting C compiler ABI info - done
[0.367s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.367s] -- Detecting C compile features
[0.367s] -- Detecting C compile features - done
[0.367s] -- Detecting CXX compiler AB<PERSON> info
[0.367s] -- Detecting CXX compiler <PERSON><PERSON> info - done
[0.367s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.367s] -- Detecting CXX compile features
[0.367s] -- Detecting CXX compile features - done
[0.367s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.681s] -- Found Python3: /home/<USER>/anaconda3/bin/python3 (found version "3.12.7") found components: Interpreter 
[0.751s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.763s] -- Found Eigen3: TRUE (found version "3.4.0") 
[0.765s] -- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)
[0.777s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.789s] [31mCMake Error at CMakeLists.txt:22 (find_package):
[0.789s]   By not providing "FindNLopt.cmake" in CMAKE_MODULE_PATH this project has
[0.789s]   asked CMake to find a package configuration file provided by "NLopt", but
[0.789s]   CMake did not find one.
[0.789s] 
[0.789s]   Could not find a package configuration file provided by "NLopt" with any of
[0.789s]   the following names:
[0.789s] 
[0.789s]     NLoptConfig.cmake
[0.789s]     nlopt-config.cmake
[0.789s] 
[0.789s]   Add the installation prefix of "NLopt" to CMAKE_PREFIX_PATH or set
[0.789s]   "NLopt_DIR" to a directory containing one of the above files.  If "NLopt"
[0.789s]   provides a separate development package or SDK, be sure it has been
[0.789s]   installed.
[0.789s] 
[0.789s] -- Configuring incomplete, errors occurred!
[0.789s] See also "/home/<USER>/test_ws/build/trac_ik_lib/CMakeFiles/CMakeOutput.log".
[0.789s] [0m

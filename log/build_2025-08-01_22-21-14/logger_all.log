[0.084s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'gimbal_service']
[0.084s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=20, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['gimbal_service'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7b2f6a331ae0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7b2f6a45ef80>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7b2f6a45ef80>>)
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.200s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/test_ws'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'ignore_ament_install'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['colcon_pkg']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'colcon_pkg'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['colcon_meta']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'colcon_meta'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['ros']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'ros'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['cmake', 'python']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'cmake'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'python'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['python_setup_py']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'python_setup_py'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'ignore_ament_install'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['colcon_pkg']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'ros'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['cmake', 'python']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'cmake'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'python'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['python_setup_py']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'python_setup_py'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'ros'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['cmake', 'python']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'cmake'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'python'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['python_setup_py']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'python_setup_py'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'ros'
[0.211s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/Paxini_Hand/dexh13_communication' with type 'ros.ament_python' and name 'dexh13_communication'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'ignore'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'colcon_meta'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extensions ['ros']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'ros'
[0.212s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/Paxini_Hand/my_interfaces' with type 'ros.ament_cmake' and name 'my_interfaces'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extensions ['colcon_meta']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'ros'
[0.213s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/Universal_Robots_ROS2_Description' with type 'ros.ament_cmake' and name 'ur_description'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'ros'
[0.213s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/arm_node' with type 'ros.ament_cmake' and name 'arm_node'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'ros'
[0.214s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/dual_arm_model' with type 'ros.ament_cmake' and name 'dual_arm_model'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'ros'
[0.215s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/dual_ur_inspire_bringup' with type 'ros.ament_cmake' and name 'dual_ur_inspire_bringup'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'ros'
[0.215s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/gimbal_service' with type 'ros.ament_python' and name 'py_srvcli'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'cmake'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'python'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['python_setup_py']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'python_setup_py'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'cmake'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'python'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['python_setup_py']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'python_setup_py'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'cmake'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'python'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['python_setup_py']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'python_setup_py'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'ros'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['cmake', 'python']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'cmake'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'ros'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['cmake', 'python']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'cmake'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'ros'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['cmake', 'python']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'cmake'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'python_setup_py'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'python_setup_py'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'python_setup_py'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'ignore_ament_install'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['colcon_pkg']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'colcon_pkg'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['colcon_meta']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'ros'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['cmake', 'python']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'cmake'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'python'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['python_setup_py']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'python_setup_py'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'ignore_ament_install'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['colcon_pkg']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'colcon_pkg'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['colcon_meta']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'ros'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['cmake', 'python']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'cmake'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'python'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['python_setup_py']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'python_setup_py'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'ignore_ament_install'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['colcon_pkg']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'colcon_pkg'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['colcon_meta']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'ros'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['cmake', 'python']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'cmake'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'python'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['python_setup_py']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'python_setup_py'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'ros'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['cmake', 'python']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'cmake'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'python'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['python_setup_py']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'python_setup_py'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'ros'
[0.220s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/inspire_hand_ros2/inspire_hand_demo' with type 'ros.ament_python' and name 'inspire_hand_demo'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extensions ['ignore', 'ignore_ament_install']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'ros'
[0.221s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces' with type 'ros.ament_cmake' and name 'inspire_hand_interfaces'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extensions ['ignore', 'ignore_ament_install']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'ros'
[0.222s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/joint_state_filter' with type 'ros.ament_cmake' and name 'joint_state_filter'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'colcon_meta'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extensions ['ros']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'ros'
[0.222s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/rtde_pkg' with type 'ros.ament_python' and name 'rtde_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'colcon_meta'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'ros'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['cmake', 'python']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'cmake'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'python'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['python_setup_py']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'python_setup_py'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extensions ['colcon_meta']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'ros'
[0.223s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/trac_ik/trac_ik' with type 'ros.ament_cmake' and name 'trac_ik'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extensions ['colcon_meta']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'ros'
[0.224s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/trac_ik/trac_ik_examples' with type 'ros.ament_cmake' and name 'trac_ik_examples'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_kinematics_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_kinematics_plugin) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_kinematics_plugin) ignored
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'ros'
[0.225s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/trac_ik/trac_ik_lib' with type 'ros.ament_cmake' and name 'trac_ik_lib'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_python) by extensions ['ignore', 'ignore_ament_install']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_python) by extension 'ignore'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_python) ignored
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extensions ['ignore', 'ignore_ament_install']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'ignore'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'ignore_ament_install'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extensions ['colcon_pkg']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'colcon_pkg'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extensions ['colcon_meta']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'colcon_meta'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extensions ['ros']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'ros'
[0.225s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/vr_camera_node' with type 'ros.ament_python' and name 'vr_camera_node'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.226s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.226s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.226s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.226s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.226s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.226s] WARNING:colcon.colcon_core.package_selection:ignoring unknown package 'gimbal_service' in --packages-select
[0.249s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'dual_arm_model' in 'dual_ur5/src/dual_arm_model'
[0.249s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'dual_ur_inspire_bringup' in 'dual_ur5/src/dual_ur_inspire_bringup'
[0.249s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'inspire_hand_interfaces' in 'dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'joint_state_filter' in 'dual_ur5/src/joint_state_filter'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'my_interfaces' in 'dual_ur5/src/Paxini_Hand/my_interfaces'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'py_srvcli' in 'dual_ur5/src/gimbal_service'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rtde_pkg' in 'dual_ur5/src/rtde_pkg'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'trac_ik_lib' in 'dual_ur5/src/trac_ik/trac_ik_lib'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ur_description' in 'dual_ur5/src/Universal_Robots_ROS2_Description'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'vr_camera_node' in 'dual_ur5/src/vr_camera_node'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'arm_node' in 'dual_ur5/src/arm_node'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'dexh13_communication' in 'dual_ur5/src/Paxini_Hand/dexh13_communication'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'inspire_hand_demo' in 'dual_ur5/src/inspire_hand_ros2/inspire_hand_demo'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'trac_ik_examples' in 'dual_ur5/src/trac_ik/trac_ik_examples'
[0.250s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'trac_ik' in 'dual_ur5/src/trac_ik/trac_ik'
[0.250s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.250s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.252s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 276 installed packages in /opt/ros/humble
[0.253s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.273s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.274s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.274s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.274s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.274s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.274s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.280s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.280s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.280s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.289s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.292s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.292s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/test_ws/install/local_setup.ps1'
[0.293s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/test_ws/install/_local_setup_util_ps1.py'
[0.294s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/test_ws/install/setup.ps1'
[0.294s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/test_ws/install/local_setup.sh'
[0.294s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/test_ws/install/_local_setup_util_sh.py'
[0.295s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/test_ws/install/setup.sh'
[0.295s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/test_ws/install/local_setup.bash'
[0.295s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/test_ws/install/setup.bash'
[0.296s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/test_ws/install/local_setup.zsh'
[0.296s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/test_ws/install/setup.zsh'

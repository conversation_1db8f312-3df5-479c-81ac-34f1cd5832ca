running egg_info
writing ../../../build/vr_camera_node/vr_camera_node.egg-info/PKG-INFO
writing dependency_links to ../../../build/vr_camera_node/vr_camera_node.egg-info/dependency_links.txt
writing entry points to ../../../build/vr_camera_node/vr_camera_node.egg-info/entry_points.txt
writing requirements to ../../../build/vr_camera_node/vr_camera_node.egg-info/requires.txt
writing top-level names to ../../../build/vr_camera_node/vr_camera_node.egg-info/top_level.txt
reading manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
writing manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info' (and everything under it)
Copying ../../../build/vr_camera_node/vr_camera_node.egg-info to /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info
running install_scripts
Installing vr_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node
Installing vr_quest3_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node
writing list of installed files to '/home/<USER>/test_ws/build/vr_camera_node/install.log'

[0.361s] Invoking command in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/vr_camera_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_camera_node build --build-base /home/<USER>/test_ws/build/vr_camera_node/build install --record /home/<USER>/test_ws/build/vr_camera_node/install.log --single-version-externally-managed install_data
[0.503s] running egg_info
[0.504s] writing ../../../build/vr_camera_node/vr_camera_node.egg-info/PKG-INFO
[0.504s] writing dependency_links to ../../../build/vr_camera_node/vr_camera_node.egg-info/dependency_links.txt
[0.504s] writing entry points to ../../../build/vr_camera_node/vr_camera_node.egg-info/entry_points.txt
[0.504s] writing requirements to ../../../build/vr_camera_node/vr_camera_node.egg-info/requires.txt
[0.504s] writing top-level names to ../../../build/vr_camera_node/vr_camera_node.egg-info/top_level.txt
[0.505s] reading manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
[0.505s] writing manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
[0.505s] running build
[0.505s] running build_py
[0.505s] running install
[0.506s] running install_lib
[0.507s] running install_data
[0.507s] running install_egg_info
[0.508s] removing '/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info' (and everything under it)
[0.508s] Copying ../../../build/vr_camera_node/vr_camera_node.egg-info to /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info
[0.508s] running install_scripts
[0.523s] Installing vr_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node
[0.523s] Installing vr_quest3_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node
[0.523s] writing list of installed files to '/home/<USER>/test_ws/build/vr_camera_node/install.log'
[0.538s] Invoked command in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node' returned '0': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/vr_camera_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_camera_node build --build-base /home/<USER>/test_ws/build/vr_camera_node/build install --record /home/<USER>/test_ws/build/vr_camera_node/install.log --single-version-externally-managed install_data

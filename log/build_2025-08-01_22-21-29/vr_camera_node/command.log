Invoking command in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/vr_camera_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_camera_node build --build-base /home/<USER>/test_ws/build/vr_camera_node/build install --record /home/<USER>/test_ws/build/vr_camera_node/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node' returned '0': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/vr_camera_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_camera_node build --build-base /home/<USER>/test_ws/build/vr_camera_node/build install --record /home/<USER>/test_ws/build/vr_camera_node/install.log --single-version-externally-managed install_data

[0.000000] (-) TimerEvent: {}
[0.000452] (-) JobUnselected: {'identifier': 'arm_node'}
[0.000532] (-) JobUnselected: {'identifier': 'dexh13_communication'}
[0.000552] (-) JobUnselected: {'identifier': 'dual_arm_model'}
[0.000571] (-) JobUnselected: {'identifier': 'dual_ur_inspire_bringup'}
[0.000598] (-) JobUnselected: {'identifier': 'inspire_hand_demo'}
[0.000615] (-) JobUnselected: {'identifier': 'inspire_hand_interfaces'}
[0.000760] (-) JobUnselected: {'identifier': 'joint_state_filter'}
[0.000776] (-) JobUnselected: {'identifier': 'my_interfaces'}
[0.000797] (-) JobUnselected: {'identifier': 'rtde_pkg'}
[0.000815] (-) JobUnselected: {'identifier': 'trac_ik'}
[0.000831] (-) JobUnselected: {'identifier': 'trac_ik_examples'}
[0.000930] (-) JobUnselected: {'identifier': 'trac_ik_lib'}
[0.000953] (-) JobUnselected: {'identifier': 'ur_description'}
[0.000972] (py_srvcli) JobQueued: {'identifier': 'py_srvcli', 'dependencies': OrderedDict()}
[0.000991] (vr_camera_node) JobQueued: {'identifier': 'vr_camera_node', 'dependencies': OrderedDict()}
[0.001174] (py_srvcli) JobStarted: {'identifier': 'py_srvcli'}
[0.005150] (vr_camera_node) JobStarted: {'identifier': 'vr_camera_node'}
[0.099905] (-) TimerEvent: {}
[0.200144] (-) TimerEvent: {}
[0.300374] (-) TimerEvent: {}
[0.400647] (-) TimerEvent: {}
[0.495346] (py_srvcli) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../../build/py_srvcli', 'build', '--build-base', '/home/<USER>/test_ws/build/py_srvcli/build', 'install', '--record', '/home/<USER>/test_ws/build/py_srvcli/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/test_ws/dual_ur5/src/gimbal_service', 'env': {'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'CONDA_PROMPT_MODIFIER': '(base)', 'LANGUAGE': 'zh_CN:en', 'USER': 'lwy', 'LC_TIME': 'zh_CN.UTF-8', 'FONTCONFIG_PATH': '/etc/fonts', 'GIO_MODULE_DIR': '/home/<USER>/snap/code/common/.cache/gio-modules', 'XDG_SESSION_TYPE': 'x11', 'GIT_ASKPASS': '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'GTK_EXE_PREFIX_VSCODE_SNAP_ORIG': '', 'GDK_BACKEND_VSCODE_SNAP_ORIG': '', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'CONDA_SHLVL': '1', 'LOCPATH_VSCODE_SNAP_ORIG': '', 'OLDPWD': '/home/<USER>/test_ws', 'TERM_PROGRAM_VERSION': '1.102.2', 'DESKTOP_SESSION': 'ubuntu', 'GTK_PATH': '/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0', 'XDG_DATA_HOME_VSCODE_SNAP_ORIG': '', 'GTK_IM_MODULE_FILE': '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache', 'GIO_LAUNCHED_DESKTOP_FILE': '/var/lib/snapd/desktop/applications/code_code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG': '', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/snap/code/201/usr/share/code/code', 'MANAGERPID': '1198', 'SYSTEMD_EXEC_PID': '1578', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', '_CE_M': '', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '54580', 'ROS_DISTRO': 'humble', 'GTK_IM_MODULE': 'ibus', 'LOGNAME': 'lwy', 'JOURNAL_STREAM': '8:9396', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_SESSION_CLASS': 'user', 'XDG_DATA_DIRS_VSCODE_SNAP_ORIG': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'USERNAME': 'lwy', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'WINDOWPATH': '2', 'PATH': '/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools', 'SESSION_MANAGER': 'local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555', 'GTK_EXE_PREFIX': '/snap/code/201/usr', 'INVOCATION_ID': '0d599cd8298c4bbe9f8e5cb2c79c41da', 'PAPERSIZE': 'a4', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'BAMF_DESKTOP_FILE_HINT': '/var/lib/snapd/desktop/applications/code_code.desktop', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LOCPATH': '/snap/code/201/usr/lib/locale', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'GIO_MODULE_DIR_VSCODE_SNAP_ORIG': '', 'XDG_DATA_HOME': '/home/<USER>/snap/code/201/.local/share', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-1be5817c21.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'GSETTINGS_SCHEMA_DIR': '/home/<USER>/anaconda3/share/glib-2.0/schemas', 'AMENT_PREFIX_PATH': '/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/anaconda3/bin/python', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'GTK_PATH_VSCODE_SNAP_ORIG': '', 'CONDA_DEFAULT_ENV': 'base', 'FONTCONFIG_FILE': '/etc/fonts/fonts.conf', 'GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG': '', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/test_ws/build/py_srvcli', 'CUDA_HOME': '/usr/local/cuda-12.1', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'CONDA_EXE': '/home/<USER>/anaconda3/bin/conda', 'XDG_DATA_DIRS': '/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/test_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'CONDA_PREFIX': '/home/<USER>/anaconda3', 'GSETTINGS_SCHEMA_DIR_CONDA_BACKUP': '/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1'}, 'shell': False}
[0.499227] (vr_camera_node) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../../build/vr_camera_node', 'build', '--build-base', '/home/<USER>/test_ws/build/vr_camera_node/build', 'install', '--record', '/home/<USER>/test_ws/build/vr_camera_node/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node', 'env': {'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'CONDA_PROMPT_MODIFIER': '(base)', 'LANGUAGE': 'zh_CN:en', 'USER': 'lwy', 'LC_TIME': 'zh_CN.UTF-8', 'FONTCONFIG_PATH': '/etc/fonts', 'GIO_MODULE_DIR': '/home/<USER>/snap/code/common/.cache/gio-modules', 'XDG_SESSION_TYPE': 'x11', 'GIT_ASKPASS': '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'GTK_EXE_PREFIX_VSCODE_SNAP_ORIG': '', 'GDK_BACKEND_VSCODE_SNAP_ORIG': '', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'CONDA_SHLVL': '1', 'LOCPATH_VSCODE_SNAP_ORIG': '', 'OLDPWD': '/home/<USER>/test_ws', 'TERM_PROGRAM_VERSION': '1.102.2', 'DESKTOP_SESSION': 'ubuntu', 'GTK_PATH': '/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0', 'XDG_DATA_HOME_VSCODE_SNAP_ORIG': '', 'GTK_IM_MODULE_FILE': '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache', 'GIO_LAUNCHED_DESKTOP_FILE': '/var/lib/snapd/desktop/applications/code_code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG': '', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/snap/code/201/usr/share/code/code', 'MANAGERPID': '1198', 'SYSTEMD_EXEC_PID': '1578', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', '_CE_M': '', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '54580', 'ROS_DISTRO': 'humble', 'GTK_IM_MODULE': 'ibus', 'LOGNAME': 'lwy', 'JOURNAL_STREAM': '8:9396', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_SESSION_CLASS': 'user', 'XDG_DATA_DIRS_VSCODE_SNAP_ORIG': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'USERNAME': 'lwy', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'WINDOWPATH': '2', 'PATH': '/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools', 'SESSION_MANAGER': 'local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555', 'GTK_EXE_PREFIX': '/snap/code/201/usr', 'INVOCATION_ID': '0d599cd8298c4bbe9f8e5cb2c79c41da', 'PAPERSIZE': 'a4', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'BAMF_DESKTOP_FILE_HINT': '/var/lib/snapd/desktop/applications/code_code.desktop', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'LOCPATH': '/snap/code/201/usr/lib/locale', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'GIO_MODULE_DIR_VSCODE_SNAP_ORIG': '', 'XDG_DATA_HOME': '/home/<USER>/snap/code/201/.local/share', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-1be5817c21.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'GSETTINGS_SCHEMA_DIR': '/home/<USER>/anaconda3/share/glib-2.0/schemas', 'AMENT_PREFIX_PATH': '/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/anaconda3/bin/python', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'GTK_PATH_VSCODE_SNAP_ORIG': '', 'CONDA_DEFAULT_ENV': 'base', 'FONTCONFIG_FILE': '/etc/fonts/fonts.conf', 'GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG': '', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/test_ws/build/vr_camera_node', 'CUDA_HOME': '/usr/local/cuda-12.1', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'CONDA_EXE': '/home/<USER>/anaconda3/bin/conda', 'XDG_DATA_DIRS': '/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/test_ws/build/vr_camera_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'CONDA_PREFIX': '/home/<USER>/anaconda3', 'GSETTINGS_SCHEMA_DIR_CONDA_BACKUP': '/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1'}, 'shell': False}
[0.500908] (-) TimerEvent: {}
[0.601213] (-) TimerEvent: {}
[0.636160] (py_srvcli) StdoutLine: {'line': b'running egg_info\n'}
[0.636462] (py_srvcli) StdoutLine: {'line': b'creating ../../../build/py_srvcli/py_srvcli.egg-info\n'}
[0.636551] (py_srvcli) StdoutLine: {'line': b'writing ../../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO\n'}
[0.636646] (py_srvcli) StdoutLine: {'line': b'writing dependency_links to ../../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt\n'}
[0.636713] (py_srvcli) StdoutLine: {'line': b'writing entry points to ../../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt\n'}
[0.636856] (py_srvcli) StdoutLine: {'line': b'writing requirements to ../../../build/py_srvcli/py_srvcli.egg-info/requires.txt\n'}
[0.636941] (py_srvcli) StdoutLine: {'line': b'writing top-level names to ../../../build/py_srvcli/py_srvcli.egg-info/top_level.txt\n'}
[0.637007] (py_srvcli) StdoutLine: {'line': b"writing manifest file '../../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'\n"}
[0.637180] (vr_camera_node) StdoutLine: {'line': b'running egg_info\n'}
[0.637488] (vr_camera_node) StdoutLine: {'line': b'writing ../../../build/vr_camera_node/vr_camera_node.egg-info/PKG-INFO\n'}
[0.637652] (vr_camera_node) StdoutLine: {'line': b'writing dependency_links to ../../../build/vr_camera_node/vr_camera_node.egg-info/dependency_links.txt\n'}
[0.637785] (py_srvcli) StdoutLine: {'line': b"reading manifest file '../../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'\n"}
[0.637890] (vr_camera_node) StdoutLine: {'line': b'writing entry points to ../../../build/vr_camera_node/vr_camera_node.egg-info/entry_points.txt\n'}
[0.637952] (vr_camera_node) StdoutLine: {'line': b'writing requirements to ../../../build/vr_camera_node/vr_camera_node.egg-info/requires.txt\n'}
[0.638014] (vr_camera_node) StdoutLine: {'line': b'writing top-level names to ../../../build/vr_camera_node/vr_camera_node.egg-info/top_level.txt\n'}
[0.638143] (py_srvcli) StdoutLine: {'line': b"writing manifest file '../../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'\n"}
[0.638262] (py_srvcli) StdoutLine: {'line': b'running build\n'}
[0.638325] (py_srvcli) StdoutLine: {'line': b'running build_py\n'}
[0.638397] (py_srvcli) StdoutLine: {'line': b'creating /home/<USER>/test_ws/build/py_srvcli/build\n'}
[0.638465] (py_srvcli) StdoutLine: {'line': b'creating /home/<USER>/test_ws/build/py_srvcli/build/lib\n'}
[0.638525] (py_srvcli) StdoutLine: {'line': b'creating /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli\n'}
[0.638589] (py_srvcli) StdoutLine: {'line': b'copying py_srvcli/gimbal_servo_service.py -> /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli\n'}
[0.638649] (py_srvcli) StdoutLine: {'line': b'copying py_srvcli/__init__.py -> /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli\n'}
[0.638741] (py_srvcli) StdoutLine: {'line': b'running install\n'}
[0.638800] (py_srvcli) StdoutLine: {'line': b'running install_lib\n'}
[0.638899] (vr_camera_node) StdoutLine: {'line': b"reading manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'\n"}
[0.638972] (py_srvcli) StdoutLine: {'line': b'creating /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli\n'}
[0.639049] (py_srvcli) StdoutLine: {'line': b'copying /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli/gimbal_servo_service.py -> /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli\n'}
[0.639115] (py_srvcli) StdoutLine: {'line': b'copying /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli/__init__.py -> /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli\n'}
[0.639274] (py_srvcli) StdoutLine: {'line': b'byte-compiling /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/gimbal_servo_service.py to gimbal_servo_service.cpython-310.pyc\n'}
[0.639673] (vr_camera_node) StdoutLine: {'line': b"writing manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'\n"}
[0.639758] (vr_camera_node) StdoutLine: {'line': b'running build\n'}
[0.639817] (vr_camera_node) StdoutLine: {'line': b'running build_py\n'}
[0.640088] (vr_camera_node) StdoutLine: {'line': b'running install\n'}
[0.640352] (vr_camera_node) StdoutLine: {'line': b'running install_lib\n'}
[0.641469] (py_srvcli) StdoutLine: {'line': b'byte-compiling /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/__init__.py to __init__.cpython-310.pyc\n'}
[0.641604] (py_srvcli) StdoutLine: {'line': b'running install_data\n'}
[0.641691] (py_srvcli) StdoutLine: {'line': b'creating /home/<USER>/test_ws/install/py_srvcli/share/ament_index\n'}
[0.641763] (vr_camera_node) StdoutLine: {'line': b'running install_data\n'}
[0.641824] (py_srvcli) StdoutLine: {'line': b'creating /home/<USER>/test_ws/install/py_srvcli/share/ament_index/resource_index\n'}
[0.641882] (py_srvcli) StdoutLine: {'line': b'creating /home/<USER>/test_ws/install/py_srvcli/share/ament_index/resource_index/packages\n'}
[0.641939] (py_srvcli) StdoutLine: {'line': b'copying resource/py_srvcli -> /home/<USER>/test_ws/install/py_srvcli/share/ament_index/resource_index/packages\n'}
[0.641996] (vr_camera_node) StdoutLine: {'line': b'running install_egg_info\n'}
[0.642059] (py_srvcli) StdoutLine: {'line': b'copying package.xml -> /home/<USER>/test_ws/install/py_srvcli/share/py_srvcli\n'}
[0.642121] (py_srvcli) StdoutLine: {'line': b'running install_egg_info\n'}
[0.642751] (py_srvcli) StdoutLine: {'line': b'Copying ../../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info\n'}
[0.643045] (py_srvcli) StdoutLine: {'line': b'running install_scripts\n'}
[0.643113] (vr_camera_node) StdoutLine: {'line': b"removing '/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info' (and everything under it)\n"}
[0.643342] (vr_camera_node) StdoutLine: {'line': b'Copying ../../../build/vr_camera_node/vr_camera_node.egg-info to /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info\n'}
[0.643801] (vr_camera_node) StdoutLine: {'line': b'running install_scripts\n'}
[0.655978] (py_srvcli) StdoutLine: {'line': b'Installing gimbal_service script to /home/<USER>/test_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.656150] (py_srvcli) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/test_ws/build/py_srvcli/install.log'\n"}
[0.656948] (vr_camera_node) StdoutLine: {'line': b'Installing vr_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node\n'}
[0.656990] (vr_camera_node) StdoutLine: {'line': b'Installing vr_quest3_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node\n'}
[0.657256] (vr_camera_node) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/test_ws/build/vr_camera_node/install.log'\n"}
[0.671093] (py_srvcli) CommandEnded: {'returncode': 0}
[0.677135] (py_srvcli) JobEnded: {'identifier': 'py_srvcli', 'rc': 0}
[0.678437] (vr_camera_node) CommandEnded: {'returncode': 0}
[0.683806] (vr_camera_node) JobEnded: {'identifier': 'vr_camera_node', 'rc': 0}
[0.684373] (-) EventReactorShutdown: {}

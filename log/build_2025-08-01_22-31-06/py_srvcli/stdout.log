running egg_info
creating ../../../build/py_srvcli/py_srvcli.egg-info
writing ../../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
writing dependency_links to ../../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
writing entry points to ../../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
writing requirements to ../../../build/py_srvcli/py_srvcli.egg-info/requires.txt
writing top-level names to ../../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
writing manifest file '../../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
reading manifest file '../../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
writing manifest file '../../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
running build
running build_py
creating /home/<USER>/test_ws/build/py_srvcli/build
creating /home/<USER>/test_ws/build/py_srvcli/build/lib
creating /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli
copying py_srvcli/gimbal_servo_service.py -> /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli
copying py_srvcli/__init__.py -> /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli
running install
running install_lib
creating /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
copying /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli/gimbal_servo_service.py -> /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
copying /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli/__init__.py -> /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
byte-compiling /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/gimbal_servo_service.py to gimbal_servo_service.cpython-310.pyc
byte-compiling /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/__init__.py to __init__.cpython-310.pyc
running install_data
creating /home/<USER>/test_ws/install/py_srvcli/share/ament_index
creating /home/<USER>/test_ws/install/py_srvcli/share/ament_index/resource_index
creating /home/<USER>/test_ws/install/py_srvcli/share/ament_index/resource_index/packages
copying resource/py_srvcli -> /home/<USER>/test_ws/install/py_srvcli/share/ament_index/resource_index/packages
copying package.xml -> /home/<USER>/test_ws/install/py_srvcli/share/py_srvcli
running install_egg_info
Copying ../../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
running install_scripts
Installing gimbal_service script to /home/<USER>/test_ws/install/py_srvcli/lib/py_srvcli
writing list of installed files to '/home/<USER>/test_ws/build/py_srvcli/install.log'

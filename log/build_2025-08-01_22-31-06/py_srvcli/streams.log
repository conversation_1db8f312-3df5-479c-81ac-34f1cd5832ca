[0.497s] Invoking command in '/home/<USER>/test_ws/dual_ur5/src/gimbal_service': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/py_srvcli build --build-base /home/<USER>/test_ws/build/py_srvcli/build install --record /home/<USER>/test_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.635s] running egg_info
[0.635s] creating ../../../build/py_srvcli/py_srvcli.egg-info
[0.635s] writing ../../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[0.635s] writing dependency_links to ../../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[0.636s] writing entry points to ../../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[0.636s] writing requirements to ../../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[0.636s] writing top-level names to ../../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[0.636s] writing manifest file '../../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.637s] reading manifest file '../../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.637s] writing manifest file '../../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.637s] running build
[0.637s] running build_py
[0.637s] creating /home/<USER>/test_ws/build/py_srvcli/build
[0.637s] creating /home/<USER>/test_ws/build/py_srvcli/build/lib
[0.637s] creating /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli
[0.637s] copying py_srvcli/gimbal_servo_service.py -> /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli
[0.638s] copying py_srvcli/__init__.py -> /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli
[0.638s] running install
[0.638s] running install_lib
[0.638s] creating /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.638s] copying /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli/gimbal_servo_service.py -> /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.638s] copying /home/<USER>/test_ws/build/py_srvcli/build/lib/py_srvcli/__init__.py -> /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.638s] byte-compiling /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/gimbal_servo_service.py to gimbal_servo_service.cpython-310.pyc
[0.640s] byte-compiling /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/__init__.py to __init__.cpython-310.pyc
[0.640s] running install_data
[0.641s] creating /home/<USER>/test_ws/install/py_srvcli/share/ament_index
[0.641s] creating /home/<USER>/test_ws/install/py_srvcli/share/ament_index/resource_index
[0.641s] creating /home/<USER>/test_ws/install/py_srvcli/share/ament_index/resource_index/packages
[0.641s] copying resource/py_srvcli -> /home/<USER>/test_ws/install/py_srvcli/share/ament_index/resource_index/packages
[0.641s] copying package.xml -> /home/<USER>/test_ws/install/py_srvcli/share/py_srvcli
[0.641s] running install_egg_info
[0.642s] Copying ../../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[0.642s] running install_scripts
[0.655s] Installing gimbal_service script to /home/<USER>/test_ws/install/py_srvcli/lib/py_srvcli
[0.655s] writing list of installed files to '/home/<USER>/test_ws/build/py_srvcli/install.log'
[0.670s] Invoked command in '/home/<USER>/test_ws/dual_ur5/src/gimbal_service' returned '0': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/py_srvcli build --build-base /home/<USER>/test_ws/build/py_srvcli/build install --record /home/<USER>/test_ws/build/py_srvcli/install.log --single-version-externally-managed install_data

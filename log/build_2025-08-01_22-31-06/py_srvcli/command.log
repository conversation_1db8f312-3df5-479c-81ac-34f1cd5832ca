Invoking command in '/home/<USER>/test_ws/dual_ur5/src/gimbal_service': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/py_srvcli build --build-base /home/<USER>/test_ws/build/py_srvcli/build install --record /home/<USER>/test_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/test_ws/dual_ur5/src/gimbal_service' returned '0': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/py_srvcli build --build-base /home/<USER>/test_ws/build/py_srvcli/build install --record /home/<USER>/test_ws/build/py_srvcli/install.log --single-version-externally-managed install_data

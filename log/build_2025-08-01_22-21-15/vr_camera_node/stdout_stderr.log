running egg_info
creating ../../../build/vr_camera_node/vr_camera_node.egg-info
writing ../../../build/vr_camera_node/vr_camera_node.egg-info/PKG-INFO
writing dependency_links to ../../../build/vr_camera_node/vr_camera_node.egg-info/dependency_links.txt
writing entry points to ../../../build/vr_camera_node/vr_camera_node.egg-info/entry_points.txt
writing requirements to ../../../build/vr_camera_node/vr_camera_node.egg-info/requires.txt
writing top-level names to ../../../build/vr_camera_node/vr_camera_node.egg-info/top_level.txt
writing manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
reading manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
writing manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
running build
running build_py
creating /home/<USER>/test_ws/build/vr_camera_node/build
creating /home/<USER>/test_ws/build/vr_camera_node/build/lib
creating /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node
copying vr_camera_node/quest3_cam_television.py -> /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node
copying vr_camera_node/television_paxini_bridge_ultimate.py -> /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node
copying vr_camera_node/__init__.py -> /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node
running install
running install_lib
creating /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node
copying /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node/quest3_cam_television.py -> /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node
copying /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node/television_paxini_bridge_ultimate.py -> /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node
copying /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node/__init__.py -> /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node
byte-compiling /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/quest3_cam_television.py to quest3_cam_television.cpython-310.pyc
byte-compiling /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/television_paxini_bridge_ultimate.py to television_paxini_bridge_ultimate.cpython-310.pyc
byte-compiling /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/__init__.py to __init__.cpython-310.pyc
running install_data
creating /home/<USER>/test_ws/install/vr_camera_node/share/ament_index
creating /home/<USER>/test_ws/install/vr_camera_node/share/ament_index/resource_index
creating /home/<USER>/test_ws/install/vr_camera_node/share/ament_index/resource_index/packages
copying resource/vr_camera_node -> /home/<USER>/test_ws/install/vr_camera_node/share/ament_index/resource_index/packages
copying package.xml -> /home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node
running install_egg_info
Copying ../../../build/vr_camera_node/vr_camera_node.egg-info to /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info
running install_scripts
Installing vr_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node
Installing vr_quest3_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node
writing list of installed files to '/home/<USER>/test_ws/build/vr_camera_node/install.log'

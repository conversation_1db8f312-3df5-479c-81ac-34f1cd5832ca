[0.380s] Invoking command in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/vr_camera_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_camera_node build --build-base /home/<USER>/test_ws/build/vr_camera_node/build install --record /home/<USER>/test_ws/build/vr_camera_node/install.log --single-version-externally-managed install_data
[0.522s] running egg_info
[0.522s] creating ../../../build/vr_camera_node/vr_camera_node.egg-info
[0.522s] writing ../../../build/vr_camera_node/vr_camera_node.egg-info/PKG-INFO
[0.522s] writing dependency_links to ../../../build/vr_camera_node/vr_camera_node.egg-info/dependency_links.txt
[0.522s] writing entry points to ../../../build/vr_camera_node/vr_camera_node.egg-info/entry_points.txt
[0.522s] writing requirements to ../../../build/vr_camera_node/vr_camera_node.egg-info/requires.txt
[0.522s] writing top-level names to ../../../build/vr_camera_node/vr_camera_node.egg-info/top_level.txt
[0.522s] writing manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
[0.523s] reading manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
[0.523s] writing manifest file '../../../build/vr_camera_node/vr_camera_node.egg-info/SOURCES.txt'
[0.524s] running build
[0.524s] running build_py
[0.524s] creating /home/<USER>/test_ws/build/vr_camera_node/build
[0.524s] creating /home/<USER>/test_ws/build/vr_camera_node/build/lib
[0.524s] creating /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node
[0.524s] copying vr_camera_node/quest3_cam_television.py -> /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node
[0.524s] copying vr_camera_node/television_paxini_bridge_ultimate.py -> /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node
[0.524s] copying vr_camera_node/__init__.py -> /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node
[0.524s] running install
[0.525s] running install_lib
[0.525s] creating /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node
[0.525s] copying /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node/quest3_cam_television.py -> /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node
[0.525s] copying /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node/television_paxini_bridge_ultimate.py -> /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node
[0.525s] copying /home/<USER>/test_ws/build/vr_camera_node/build/lib/vr_camera_node/__init__.py -> /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node
[0.526s] byte-compiling /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/quest3_cam_television.py to quest3_cam_television.cpython-310.pyc
[0.528s] byte-compiling /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/television_paxini_bridge_ultimate.py to television_paxini_bridge_ultimate.cpython-310.pyc
[0.531s] byte-compiling /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/__init__.py to __init__.cpython-310.pyc
[0.531s] running install_data
[0.531s] creating /home/<USER>/test_ws/install/vr_camera_node/share/ament_index
[0.531s] creating /home/<USER>/test_ws/install/vr_camera_node/share/ament_index/resource_index
[0.531s] creating /home/<USER>/test_ws/install/vr_camera_node/share/ament_index/resource_index/packages
[0.531s] copying resource/vr_camera_node -> /home/<USER>/test_ws/install/vr_camera_node/share/ament_index/resource_index/packages
[0.531s] copying package.xml -> /home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node
[0.531s] running install_egg_info
[0.532s] Copying ../../../build/vr_camera_node/vr_camera_node.egg-info to /home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info
[0.532s] running install_scripts
[0.546s] Installing vr_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node
[0.546s] Installing vr_quest3_cam_node script to /home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node
[0.546s] writing list of installed files to '/home/<USER>/test_ws/build/vr_camera_node/install.log'
[0.560s] Invoked command in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node' returned '0': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/vr_camera_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_camera_node build --build-base /home/<USER>/test_ws/build/vr_camera_node/build install --record /home/<USER>/test_ws/build/vr_camera_node/install.log --single-version-externally-managed install_data

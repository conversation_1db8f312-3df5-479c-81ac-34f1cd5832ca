[0.061s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'gimbal_service', 'vr_camera_node']
[0.061s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=20, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['gimbal_service', 'vr_camera_node'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7c9ef5b51b10>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7c9ef5c5efb0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7c9ef5c5efb0>>)
[0.153s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.153s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.153s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.153s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.153s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.153s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.153s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/test_ws'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['ignore', 'ignore_ament_install']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'ignore'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'ignore_ament_install'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['colcon_pkg']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'colcon_pkg'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['colcon_meta']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'ros'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['cmake', 'python']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'cmake'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'python'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extensions ['python_setup_py']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5) by extension 'python_setup_py'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['ignore', 'ignore_ament_install']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'ignore'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'ignore_ament_install'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['colcon_pkg']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'colcon_pkg'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['colcon_meta']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'ros'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['cmake', 'python']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'cmake'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'python'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extensions ['python_setup_py']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src) by extension 'python_setup_py'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['ignore', 'ignore_ament_install']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'ignore'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'ignore_ament_install'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['colcon_pkg']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'colcon_pkg'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['colcon_meta']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'ros'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['cmake', 'python']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'cmake'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'python'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extensions ['python_setup_py']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand) by extension 'python_setup_py'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extensions ['ignore', 'ignore_ament_install']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'ignore'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'ignore_ament_install'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extensions ['colcon_pkg']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'colcon_pkg'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extensions ['colcon_meta']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'colcon_meta'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extensions ['ros']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/dexh13_communication) by extension 'ros'
[0.163s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/Paxini_Hand/dexh13_communication' with type 'ros.ament_python' and name 'dexh13_communication'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extensions ['ignore', 'ignore_ament_install']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'ignore'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'ignore_ament_install'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extensions ['colcon_pkg']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'colcon_pkg'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extensions ['colcon_meta']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'colcon_meta'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extensions ['ros']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Paxini_Hand/my_interfaces) by extension 'ros'
[0.164s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/Paxini_Hand/my_interfaces' with type 'ros.ament_cmake' and name 'my_interfaces'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extensions ['ignore', 'ignore_ament_install']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'ignore'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'ignore_ament_install'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extensions ['colcon_pkg']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'colcon_pkg'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extensions ['colcon_meta']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'colcon_meta'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extensions ['ros']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/Universal_Robots_ROS2_Description) by extension 'ros'
[0.165s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/Universal_Robots_ROS2_Description' with type 'ros.ament_cmake' and name 'ur_description'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extensions ['ignore', 'ignore_ament_install']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'ignore'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'ignore_ament_install'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extensions ['colcon_pkg']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'colcon_pkg'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extensions ['colcon_meta']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'colcon_meta'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extensions ['ros']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/arm_node) by extension 'ros'
[0.166s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/arm_node' with type 'ros.ament_cmake' and name 'arm_node'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'ignore_ament_install'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extensions ['colcon_pkg']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'colcon_pkg'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extensions ['colcon_meta']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'colcon_meta'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extensions ['ros']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_arm_model) by extension 'ros'
[0.166s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/dual_arm_model' with type 'ros.ament_cmake' and name 'dual_arm_model'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'ignore_ament_install'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extensions ['colcon_pkg']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'colcon_pkg'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extensions ['colcon_meta']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'colcon_meta'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extensions ['ros']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/dual_ur_inspire_bringup) by extension 'ros'
[0.167s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/dual_ur_inspire_bringup' with type 'ros.ament_cmake' and name 'dual_ur_inspire_bringup'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'ignore_ament_install'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extensions ['colcon_pkg']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'colcon_pkg'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extensions ['colcon_meta']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'colcon_meta'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extensions ['ros']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/gimbal_service) by extension 'ros'
[0.168s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/gimbal_service' with type 'ros.ament_python' and name 'py_srvcli'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'colcon_pkg'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['colcon_meta']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'colcon_meta'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['ros']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'ros'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['cmake', 'python']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'cmake'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'python'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extensions ['python_setup_py']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2) by extension 'python_setup_py'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'colcon_pkg'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['colcon_meta']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'colcon_meta'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['ros']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'ros'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['cmake', 'python']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'cmake'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'python'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extensions ['python_setup_py']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision) by extension 'python_setup_py'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'colcon_pkg'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['colcon_meta']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'colcon_meta'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['ros']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'ros'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['cmake', 'python']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'cmake'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'python'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extensions ['python_setup_py']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/act) by extension 'python_setup_py'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['ignore', 'ignore_ament_install']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'ignore'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'ignore_ament_install'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['colcon_pkg']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['ros']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'ros'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['cmake', 'python']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'cmake'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'python'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extensions ['python_setup_py']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/img) by extension 'python_setup_py'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'ignore'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'ignore_ament_install'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['colcon_pkg']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['ros']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'ros'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['cmake', 'python']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'cmake'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'python'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extensions ['python_setup_py']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/scripts) by extension 'python_setup_py'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['ignore', 'ignore_ament_install']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'ignore'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'ignore_ament_install'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['colcon_pkg']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/__pycache__) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/dynamixel) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['cmake', 'python']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'cmake'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'python'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extensions ['python_setup_py']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc) by extension 'python_setup_py'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['cmake', 'python']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'cmake'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'python'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extensions ['python_setup_py']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/TeleVision/teleop/webrtc/__pycache__) by extension 'python_setup_py'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['cmake', 'python']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'cmake'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'python'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extensions ['python_setup_py']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget) by extension 'python_setup_py'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'cmake'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'python'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extensions ['python_setup_py']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/__pycache__) by extension 'python_setup_py'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'cmake'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'python'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extensions ['python_setup_py']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/ins-dex-retarget/assets) by extension 'python_setup_py'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_demo) by extension 'ros'
[0.173s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/inspire_hand_ros2/inspire_hand_demo' with type 'ros.ament_python' and name 'inspire_hand_demo'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces) by extension 'ros'
[0.173s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces' with type 'ros.ament_cmake' and name 'inspire_hand_interfaces'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'ignore_ament_install'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extensions ['colcon_pkg']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'colcon_pkg'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extensions ['colcon_meta']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'colcon_meta'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extensions ['ros']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/joint_state_filter) by extension 'ros'
[0.174s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/joint_state_filter' with type 'ros.ament_cmake' and name 'joint_state_filter'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'ignore'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'ignore_ament_install'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extensions ['colcon_pkg']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'colcon_pkg'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extensions ['colcon_meta']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'colcon_meta'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extensions ['ros']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/rtde_pkg) by extension 'ros'
[0.175s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/rtde_pkg' with type 'ros.ament_python' and name 'rtde_pkg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'ignore_ament_install'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['colcon_pkg']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'colcon_pkg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['colcon_meta']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'colcon_meta'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['ros']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'ros'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['cmake', 'python']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'cmake'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'python'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extensions ['python_setup_py']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik) by extension 'python_setup_py'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'ignore_ament_install'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extensions ['colcon_pkg']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'colcon_pkg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extensions ['colcon_meta']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'colcon_meta'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extensions ['ros']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik) by extension 'ros'
[0.176s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/trac_ik/trac_ik' with type 'ros.ament_cmake' and name 'trac_ik'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_examples) by extension 'ros'
[0.176s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/trac_ik/trac_ik_examples' with type 'ros.ament_cmake' and name 'trac_ik_examples'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_kinematics_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_kinematics_plugin) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_kinematics_plugin) ignored
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_lib) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/trac_ik/trac_ik_lib' with type 'ros.ament_cmake' and name 'trac_ik_lib'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_python) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_python) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/trac_ik/trac_ik_python) ignored
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(dual_ur5/src/vr_camera_node) by extension 'ros'
[0.178s] DEBUG:colcon.colcon_core.package_identification:Package 'dual_ur5/src/vr_camera_node' with type 'ros.ament_python' and name 'vr_camera_node'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.178s] WARNING:colcon.colcon_core.package_selection:ignoring unknown package 'gimbal_service' in --packages-select
[0.199s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'dual_arm_model' in 'dual_ur5/src/dual_arm_model'
[0.199s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'dual_ur_inspire_bringup' in 'dual_ur5/src/dual_ur_inspire_bringup'
[0.199s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'inspire_hand_interfaces' in 'dual_ur5/src/inspire_hand_ros2/inspire_hand_interfaces'
[0.199s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'joint_state_filter' in 'dual_ur5/src/joint_state_filter'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'my_interfaces' in 'dual_ur5/src/Paxini_Hand/my_interfaces'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'py_srvcli' in 'dual_ur5/src/gimbal_service'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rtde_pkg' in 'dual_ur5/src/rtde_pkg'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'trac_ik_lib' in 'dual_ur5/src/trac_ik/trac_ik_lib'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ur_description' in 'dual_ur5/src/Universal_Robots_ROS2_Description'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'arm_node' in 'dual_ur5/src/arm_node'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'dexh13_communication' in 'dual_ur5/src/Paxini_Hand/dexh13_communication'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'inspire_hand_demo' in 'dual_ur5/src/inspire_hand_ros2/inspire_hand_demo'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'trac_ik_examples' in 'dual_ur5/src/trac_ik/trac_ik_examples'
[0.200s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'trac_ik' in 'dual_ur5/src/trac_ik/trac_ik'
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.201s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 276 installed packages in /opt/ros/humble
[0.202s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.223s] Level 5:colcon.colcon_core.verb:set package 'vr_camera_node' build argument 'cmake_args' from command line to 'None'
[0.223s] Level 5:colcon.colcon_core.verb:set package 'vr_camera_node' build argument 'cmake_target' from command line to 'None'
[0.223s] Level 5:colcon.colcon_core.verb:set package 'vr_camera_node' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.223s] Level 5:colcon.colcon_core.verb:set package 'vr_camera_node' build argument 'cmake_clean_cache' from command line to 'False'
[0.223s] Level 5:colcon.colcon_core.verb:set package 'vr_camera_node' build argument 'cmake_clean_first' from command line to 'False'
[0.223s] Level 5:colcon.colcon_core.verb:set package 'vr_camera_node' build argument 'cmake_force_configure' from command line to 'False'
[0.223s] Level 5:colcon.colcon_core.verb:set package 'vr_camera_node' build argument 'ament_cmake_args' from command line to 'None'
[0.223s] Level 5:colcon.colcon_core.verb:set package 'vr_camera_node' build argument 'catkin_cmake_args' from command line to 'None'
[0.223s] Level 5:colcon.colcon_core.verb:set package 'vr_camera_node' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.223s] DEBUG:colcon.colcon_core.verb:Building package 'vr_camera_node' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/test_ws/build/vr_camera_node', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/test_ws/install/vr_camera_node', 'merge_install': False, 'path': '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node', 'symlink_install': False, 'test_result_base': None}
[0.223s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.224s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.224s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node' with build type 'ament_python'
[0.224s] Level 1:colcon.colcon_core.shell:create_environment_hook('vr_camera_node', 'ament_prefix_path')
[0.226s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.226s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/hook/ament_prefix_path.ps1'
[0.226s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/hook/ament_prefix_path.dsv'
[0.226s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/hook/ament_prefix_path.sh'
[0.227s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.227s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.386s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node'
[0.386s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.386s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.606s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/vr_camera_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_camera_node build --build-base /home/<USER>/test_ws/build/vr_camera_node/build install --record /home/<USER>/test_ws/build/vr_camera_node/install.log --single-version-externally-managed install_data
[0.785s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/test_ws/dual_ur5/src/vr_camera_node' returned '0': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/test_ws/build/vr_camera_node/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_camera_node build --build-base /home/<USER>/test_ws/build/vr_camera_node/build install --record /home/<USER>/test_ws/build/vr_camera_node/install.log --single-version-externally-managed install_data
[0.788s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/test_ws/install/vr_camera_node' for CMake module files
[0.788s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/test_ws/install/vr_camera_node' for CMake config files
[0.789s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/test_ws/install/vr_camera_node/lib'
[0.789s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/test_ws/install/vr_camera_node/bin'
[0.789s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/test_ws/install/vr_camera_node/lib/pkgconfig/vr_camera_node.pc'
[0.789s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages'
[0.789s] Level 1:colcon.colcon_core.shell:create_environment_hook('vr_camera_node', 'pythonpath')
[0.790s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/hook/pythonpath.ps1'
[0.790s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/hook/pythonpath.dsv'
[0.790s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/hook/pythonpath.sh'
[0.790s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/test_ws/install/vr_camera_node/bin'
[0.790s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(vr_camera_node)
[0.791s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/package.ps1'
[0.791s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/package.dsv'
[0.791s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/package.sh'
[0.792s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/package.bash'
[0.792s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/package.zsh'
[0.792s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/test_ws/install/vr_camera_node/share/colcon-core/packages/vr_camera_node)
[0.793s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.793s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.793s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.793s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.796s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.796s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.796s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.800s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.801s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/test_ws/install/local_setup.ps1'
[0.801s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/test_ws/install/_local_setup_util_ps1.py'
[0.802s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/test_ws/install/setup.ps1'
[0.803s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/test_ws/install/local_setup.sh'
[0.803s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/test_ws/install/_local_setup_util_sh.py'
[0.803s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/test_ws/install/setup.sh'
[0.804s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/test_ws/install/local_setup.bash'
[0.804s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/test_ws/install/setup.bash'
[0.805s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/test_ws/install/local_setup.zsh'
[0.805s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/test_ws/install/setup.zsh'

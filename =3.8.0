Collecting vuer==0.0.32rc7
  Using cached vuer-0.0.32rc7-py3-none-any.whl.metadata (5.3 kB)
Requirement already satisfied: aiohttp in /home/<USER>/anaconda3/lib/python3.12/site-packages (3.10.5)
Collecting aiohttp_cors==0.7.0
  Downloading aiohttp_cors-0.7.0-py3-none-any.whl.metadata (20 kB)
Collecting aiortc==1.8.0
  Downloading aiortc-1.8.0-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.5 kB)
Collecting av==11.0.0
  Downloading av-11.0.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.5 kB)
Collecting pytransform3d==3.5.0
  Downloading pytransform3d-3.5.0.tar.gz (102 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting params-proto>=2.11.16 (from vuer==0.0.32rc7)
  Using cached params_proto-2.13.2-py3-none-any.whl.metadata (8.7 kB)
Requirement already satisfied: pillow in /home/<USER>/anaconda3/lib/python3.12/site-packages (from vuer==0.0.32rc7) (10.4.0)
Requirement already satisfied: msgpack in /home/<USER>/anaconda3/lib/python3.12/site-packages (from vuer==0.0.32rc7) (1.0.3)
Requirement already satisfied: numpy>=1.21 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from vuer==0.0.32rc7) (2.3.1)
Collecting websockets (from vuer==0.0.32rc7)
  Using cached websockets-15.0.1-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Collecting aioice<1.0.0,>=0.9.0 (from aiortc==1.8.0)
  Downloading aioice-0.10.1-py3-none-any.whl.metadata (4.1 kB)
Requirement already satisfied: cffi>=1.0.0 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from aiortc==1.8.0) (1.17.1)
Requirement already satisfied: cryptography>=42.0.0 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from aiortc==1.8.0) (43.0.0)
Collecting google-crc32c>=1.1 (from aiortc==1.8.0)
  Downloading google_crc32c-1.7.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.3 kB)
Collecting pyee>=9.0.0 (from aiortc==1.8.0)
  Downloading pyee-13.0.0-py3-none-any.whl.metadata (2.9 kB)
Collecting pylibsrtp>=0.10.0 (from aiortc==1.8.0)
  Downloading pylibsrtp-0.12.0-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.1 kB)
Requirement already satisfied: pyopenssl>=24.0.0 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from aiortc==1.8.0) (24.2.1)
Requirement already satisfied: scipy in /home/<USER>/anaconda3/lib/python3.12/site-packages (from pytransform3d==3.5.0) (1.13.1)
Requirement already satisfied: matplotlib in /home/<USER>/anaconda3/lib/python3.12/site-packages (from pytransform3d==3.5.0) (3.9.2)
Requirement already satisfied: lxml in /home/<USER>/anaconda3/lib/python3.12/site-packages (from pytransform3d==3.5.0) (5.2.1)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from aiohttp) (2.4.0)
Requirement already satisfied: aiosignal>=1.1.2 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from aiohttp) (1.2.0)
Requirement already satisfied: attrs>=17.3.0 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from aiohttp) (23.1.0)
Requirement already satisfied: frozenlist>=1.1.1 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from aiohttp) (1.4.0)
Requirement already satisfied: multidict<7.0,>=4.5 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from aiohttp) (6.0.4)
Requirement already satisfied: yarl<2.0,>=1.0 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from aiohttp) (1.11.0)
Collecting dnspython>=2.0.0 (from aioice<1.0.0,>=0.9.0->aiortc==1.8.0)
  Downloading dnspython-2.7.0-py3-none-any.whl.metadata (5.8 kB)
Collecting ifaddr>=0.2.0 (from aioice<1.0.0,>=0.9.0->aiortc==1.8.0)
  Downloading ifaddr-0.2.0-py3-none-any.whl.metadata (4.9 kB)
Requirement already satisfied: pycparser in /home/<USER>/anaconda3/lib/python3.12/site-packages (from cffi>=1.0.0->aiortc==1.8.0) (2.21)
Collecting waterbear>=2.6.8 (from params-proto>=2.11.16->vuer==0.0.32rc7)
  Using cached waterbear-2.6.8-py3-none-any.whl.metadata (9.8 kB)
Collecting argparse (from params-proto>=2.11.16->vuer==0.0.32rc7)
  Using cached argparse-1.4.0-py2.py3-none-any.whl.metadata (2.8 kB)
Collecting argcomplete (from params-proto>=2.11.16->vuer==0.0.32rc7)
  Using cached argcomplete-3.6.2-py3-none-any.whl.metadata (16 kB)
Collecting expandvars (from params-proto>=2.11.16->vuer==0.0.32rc7)
  Downloading expandvars-1.1.1-py3-none-any.whl.metadata (6.2 kB)
Collecting termcolor (from params-proto>=2.11.16->vuer==0.0.32rc7)
  Using cached termcolor-3.1.0-py3-none-any.whl.metadata (6.4 kB)
Requirement already satisfied: typing-extensions in /home/<USER>/anaconda3/lib/python3.12/site-packages (from pyee>=9.0.0->aiortc==1.8.0) (4.11.0)
Requirement already satisfied: idna>=2.0 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from yarl<2.0,>=1.0->aiohttp) (3.7)
Requirement already satisfied: contourpy>=1.0.1 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from matplotlib->pytransform3d==3.5.0) (1.2.0)
Requirement already satisfied: cycler>=0.10 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from matplotlib->pytransform3d==3.5.0) (0.11.0)
Requirement already satisfied: fonttools>=4.22.0 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from matplotlib->pytransform3d==3.5.0) (4.51.0)
Requirement already satisfied: kiwisolver>=1.3.1 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from matplotlib->pytransform3d==3.5.0) (1.4.4)
Requirement already satisfied: packaging>=20.0 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from matplotlib->pytransform3d==3.5.0) (24.1)
Requirement already satisfied: pyparsing>=2.3.1 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from matplotlib->pytransform3d==3.5.0) (3.1.2)
Requirement already satisfied: python-dateutil>=2.7 in /home/<USER>/anaconda3/lib/python3.12/site-packages (from matplotlib->pytransform3d==3.5.0) (2.9.0.post0)

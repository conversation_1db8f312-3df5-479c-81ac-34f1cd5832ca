#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
import time
import threading
import struct
import sys

# 尝试多种方式导入serial模块
try:
    import serial
except ImportError:
    try:
        import pyserial as serial
    except ImportError:
        print("错误: 无法导入serial模块。请确保已安装pyserial:")
        print("    pip install pyserial")
        print("或者:")
        print("    sudo apt install python3-serial")
        sys.exit(1)

from example_interfaces.srv import AddTwoInts

class GimbalServoService(Node):
    def __init__(self):
        super().__init__('gimbal_servo_service')

        # 参数配置
        self.declare_parameter('port', '/dev/ttyUSB0')
        self.declare_parameter('baudrate', 115200)
        self.declare_parameter('default_move_time', 80)   # 增加移动时间到100ms，减少舵机负载
        self.declare_parameter('default_power', 700)  # 降低功率到600mW，减少发热
        self.declare_parameter('angle_threshold', 1.0)  # 增加角度变化阈值，减少无效命令
        self.declare_parameter('max_same_commands', 5)   # 增加最大相同命令次数
        self.declare_parameter('command_rate_limit', 20)  # 新增：命令发送频率限制(Hz)
        self.declare_parameter('buffer_clear_interval', 50)  # 新增：缓冲区清理间隔(命令数)

        # 云台舵机角度限制参数 - 减少极限角度，避免舵机过热
        self.declare_parameter('pitch_min_angle', -60.0)  # 俯仰最小角度（减少到60度）
        self.declare_parameter('pitch_max_angle', 60.0)   # 俯仰最大角度（减少到60度）
        self.declare_parameter('yaw_min_angle', -90.0)    # 偏航最小角度（减少到90度）
        self.declare_parameter('yaw_max_angle', 90.0)     # 偏航最大角度（减少到90度）

        # 获取参数
        port = self.get_parameter('port').value
        baudrate = self.get_parameter('baudrate').value
        self.default_move_time = self.get_parameter('default_move_time').value
        self.default_power = self.get_parameter('default_power').value
        self.angle_threshold = self.get_parameter('angle_threshold').value
        self.max_same_commands = self.get_parameter('max_same_commands').value
        self.command_rate_limit = self.get_parameter('command_rate_limit').value
        self.buffer_clear_interval = self.get_parameter('buffer_clear_interval').value

        self.pitch_min_angle = self.get_parameter('pitch_min_angle').value
        self.pitch_max_angle = self.get_parameter('pitch_max_angle').value
        self.yaw_min_angle = self.get_parameter('yaw_min_angle').value
        self.yaw_max_angle = self.get_parameter('yaw_max_angle').value

        # 舵机ID定义（根据实际硬件测试确认）
        self.YAW_SERVO_ID = 0    # 下层舵机 - 偏航控制（左右）
        self.PITCH_SERVO_ID = 1  # 上层舵机 - 俯仰控制（上下）

        # 角度过滤和防抖动机制
        self.last_yaw_angle = None
        self.last_pitch_angle = None
        self.same_command_count = 0
        self.command_count = 0

        # 舵机保护机制
        self.extreme_angle_count = 0  # 极限角度计数
        self.extreme_angle_threshold = 100  # 极限角度阈值（100次后强制复位）
        self.extreme_angle_limit = 90.0  # 定义极限角度（超过45度认为是极限）

        # 新增：频率控制和缓冲区管理
        self.last_command_time = 0.0
        self.min_command_interval = 1.0 / self.command_rate_limit  # 最小命令间隔
        self.buffer_clear_counter = 0
        self.consecutive_failures = 0  # 连续失败计数
        self.max_consecutive_failures = 5  # 最大连续失败次数

        # 初始化串口
        self.serial_port = None
        self.serial_lock = threading.Lock()
        self.running = True
        self.servo_connection_status = {0: False, 1: False}  # 舵机连接状态
        
        try:
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.2,  # 增加超时时间
                write_timeout=0.2  # 添加写入超时
            )
            # 清空缓冲区
            self.serial_port.reset_input_buffer()
            self.serial_port.reset_output_buffer()
            time.sleep(0.5)
            self.get_logger().info(f"已连接串口 {port}，波特率 {baudrate}")

            # 测试舵机连接并配置角度限制
            self.initialize_servos()
                
        except Exception as e:
            self.get_logger().error(f"串口连接错误: {str(e)}")
            raise
        
        # 创建服务
        self.srv = self.create_service(
            AddTwoInts,
            'set_gimbal_angles',
            self.set_gimbal_angles_callback)

        # 创建复位服务
        self.reset_srv = self.create_service(
            AddTwoInts,
            'reset_gimbal',
            self.reset_gimbal_callback)
        
        self.get_logger().info("云台舵机控制服务已就绪")
        self.get_logger().info(f"俯仰角度范围: [{self.pitch_min_angle}, {self.pitch_max_angle}]")
        self.get_logger().info(f"偏航角度范围: [{self.yaw_min_angle}, {self.yaw_max_angle}]")
    
    def initialize_servos(self):
        """初始化舵机连接并配置角度限制"""
        # 配置俯仰舵机角度限制
        if self.configure_angle_limits(self.PITCH_SERVO_ID, self.pitch_min_angle, self.pitch_max_angle):
            self.servo_connection_status[self.PITCH_SERVO_ID] = True
            self.get_logger().info(f"俯仰舵机 (ID {self.PITCH_SERVO_ID}) 已初始化，角度限制: {self.pitch_min_angle}° 至 {self.pitch_max_angle}°")
        
        # 配置偏航舵机角度限制
        if self.configure_angle_limits(self.YAW_SERVO_ID, self.yaw_min_angle, self.yaw_max_angle):
            self.servo_connection_status[self.YAW_SERVO_ID] = True
            self.get_logger().info(f"偏航舵机 (ID {self.YAW_SERVO_ID}) 已初始化，角度限制: {self.yaw_min_angle}° 至 {self.yaw_max_angle}°")
    
    def configure_angle_limits(self, servo_id, min_angle, max_angle):
        """配置舵机角度限制"""
        # 1. 开启角度限制开关 (data_id=48, value=0x01)
        if not self.write_servo_parameter(servo_id, 48, 0x01):
            self.get_logger().error(f"无法启用舵机 ID {servo_id} 的角度限制")
            return False
        
        # 2. 设置角度下限 (data_id=52)
        min_angle_int = int(min_angle * 10)  # 转换为0.1度单位
        if not self.write_servo_parameter(servo_id, 52, min_angle_int):
            self.get_logger().error(f"无法设置舵机 ID {servo_id} 的最小角度")
            return False
        
        # 3. 设置角度上限 (data_id=51)
        max_angle_int = int(max_angle * 10)  # 转换为0.1度单位
        if not self.write_servo_parameter(servo_id, 51, max_angle_int):
            self.get_logger().error(f"无法设置舵机 ID {servo_id} 的最大角度")
            return False
        
        return True
    
    def write_servo_parameter(self, servo_id, data_id, value):
        """写入舵机参数 (指令ID=4 WRITE_DATA)"""
        # 构建内容: [servo_id, data_id, value(2字节小端)]
        content = servo_id.to_bytes(1, 'little')
        content += data_id.to_bytes(1, 'little')
        
        # 根据参数类型确定字节长度
        if data_id in [48, 49]:  # 单字节参数
            content += value.to_bytes(1, 'little')
        else:  # 双字节参数
            content += value.to_bytes(2, 'little', signed=True)
        
        with self.serial_lock:
            command = self.build_command(0x04, content)  # WRITE_DATA指令
            
            try:
                self.serial_port.write(command)
                self.get_logger().debug(f"参数写入命令已发送到 ID {servo_id}: {command.hex(' ')}")
                
                # 等待配置生效
                time.sleep(0.1)
                return True
            except Exception as e:
                self.get_logger().error(f"参数写入错误: {str(e)}")
                return False
    
    def set_gimbal_angles_callback(self, request, response):
        """服务回调函数，设置云台角度"""
        # 解析请求中的角度值（已转换为浮点数）
        yaw_angle = request.a / 100.0    # 第一个参数是偏航角度
        pitch_angle = request.b / 100.0  # 第二个参数是俯仰角度

        self.command_count += 1
        current_time = time.time()

        # 频率控制：检查是否满足最小命令间隔
        if current_time - self.last_command_time < self.min_command_interval:
            self.get_logger().debug(f"⏸️  命令频率过高，跳过 #{self.command_count}")
            response.sum = 1  # 返回成功，但实际没有发送
            return response

        self.get_logger().info(f"收到云台控制请求 #{self.command_count}: 偏航={yaw_angle}°, 俯仰={pitch_angle}°")

        # 应用角度限制
        pitch_angle = max(self.pitch_min_angle, min(self.pitch_max_angle, pitch_angle))
        yaw_angle = max(self.yaw_min_angle, min(self.yaw_max_angle, yaw_angle))

        # 智能角度过滤 - 防止重复发送相同角度
        should_send = self.should_send_command(yaw_angle, pitch_angle)

        if not should_send:
            self.get_logger().debug(f"⏸️  跳过重复角度命令 #{self.same_command_count}: 偏航={yaw_angle}°, 俯仰={pitch_angle}°")
            response.sum = 1  # 返回成功，但实际没有发送
            return response

        self.get_logger().info(f"执行云台控制: 偏航舵机 (ID{self.YAW_SERVO_ID}) 到 {yaw_angle}°, 俯仰舵机 (ID{self.PITCH_SERVO_ID}) 到 {-pitch_angle}°")

        # 同时控制两个舵机（pitch角度需要反转）
        success_pitch = self.set_servo_angle(self.PITCH_SERVO_ID, -pitch_angle)  # 反转pitch方向
        success_yaw = self.set_servo_angle(self.YAW_SERVO_ID, yaw_angle)

        # 更新时间戳
        self.last_command_time = current_time

        # 缓冲区管理：定期清理串口缓冲区
        self.buffer_clear_counter += 1
        if self.buffer_clear_counter >= self.buffer_clear_interval:
            self.clear_serial_buffers()
            self.buffer_clear_counter = 0

        # 更新上次发送的角度
        self.last_yaw_angle = yaw_angle
        self.last_pitch_angle = pitch_angle

        if not success_pitch or not success_yaw:
            self.consecutive_failures += 1
            self.get_logger().warn(f"舵机控制失败 (连续失败: {self.consecutive_failures})")

            # 连续失败过多时尝试恢复
            if self.consecutive_failures >= self.max_consecutive_failures:
                self.get_logger().error("连续失败过多，尝试恢复连接...")
                self.recover_connection()

            self.check_servo_connection()
            response.sum = 0  # 表示失败
        else:
            self.consecutive_failures = 0  # 重置失败计数
            response.sum = 1  # 表示成功

        return response

    def reset_gimbal_callback(self, request, response):
        """复位云台服务回调函数"""
        self.get_logger().info("收到云台复位请求")

        success = self.reset_servos_to_center()
        response.sum = 1 if success else 0

        return response

    def reset_servos_to_center(self):
        """将舵机复位到中心位置"""
        self.get_logger().info("🔄 复位舵机到中心位置...")

        # 发送中心位置命令
        success_yaw = self.set_servo_angle(self.YAW_SERVO_ID, 0.0)
        success_pitch = self.set_servo_angle(self.PITCH_SERVO_ID, 0.0)

        if success_yaw and success_pitch:
            self.get_logger().info("✅ 舵机复位成功")
            # 重置计数器
            self.same_command_count = 0
            self.last_yaw_angle = 0.0
            self.last_pitch_angle = 0.0
        else:
            self.get_logger().error("❌ 舵机复位失败")

        return success_yaw and success_pitch

    def should_send_command(self, yaw_angle, pitch_angle):
        """判断是否应该发送命令 - 智能过滤重复角度和极限角度保护"""
        # 检查是否为极限角度
        is_extreme_yaw = abs(yaw_angle) > self.extreme_angle_limit
        is_extreme_pitch = abs(pitch_angle) > self.extreme_angle_limit

        if is_extreme_yaw or is_extreme_pitch:
            self.extreme_angle_count += 1
            if self.extreme_angle_count > self.extreme_angle_threshold:
                self.get_logger().warn(f"⚠️  检测到长时间极限角度 (yaw={yaw_angle:.1f}°, pitch={pitch_angle:.1f}°)，强制复位到中心位置")
                # 强制复位到中心位置
                self.reset_servos_to_center()
                return False
            elif self.extreme_angle_count % 20 == 0:
                self.get_logger().warn(f"⚠️  极限角度警告 #{self.extreme_angle_count}: yaw={yaw_angle:.1f}°, pitch={pitch_angle:.1f}°")
        else:
            # 不是极限角度，重置计数
            self.extreme_angle_count = 0

        # 如果是第一次发送，直接发送
        if self.last_yaw_angle is None or self.last_pitch_angle is None:
            self.same_command_count = 0
            return True

        # 计算角度变化
        yaw_diff = abs(yaw_angle - self.last_yaw_angle)
        pitch_diff = abs(pitch_angle - self.last_pitch_angle)

        # 如果角度变化超过阈值，发送命令
        if yaw_diff >= self.angle_threshold or pitch_diff >= self.angle_threshold:
            self.same_command_count = 0
            return True

        # 角度变化很小，增加相同命令计数
        self.same_command_count += 1

        # 如果连续相同命令次数超过限制，跳过发送
        if self.same_command_count > self.max_same_commands:
            # 每50次打印一次跳过信息
            if self.same_command_count % 50 == 0:
                self.get_logger().info(f"⏸️  已跳过 {self.same_command_count} 次重复角度命令")
            return False

        # 前几次相同命令仍然发送（确保舵机到位）
        return True

    def check_servo_connection(self):
        """检查舵机连接状态"""
        self.get_logger().warn("检查舵机连接状态...")
        
        # 检查俯仰舵机
        if self.ping_servo(self.PITCH_SERVO_ID):
            if not self.servo_connection_status[self.PITCH_SERVO_ID]:
                self.servo_connection_status[self.PITCH_SERVO_ID] = True
                self.get_logger().info(f"俯仰舵机 (ID {self.PITCH_SERVO_ID}) 现已在线")
        else:
            self.servo_connection_status[self.PITCH_SERVO_ID] = False
            self.get_logger().error(f"俯仰舵机 (ID {self.PITCH_SERVO_ID}) 无响应!")
        
        # 检查偏航舵机
        if self.ping_servo(self.YAW_SERVO_ID):
            if not self.servo_connection_status[self.YAW_SERVO_ID]:
                self.servo_connection_status[self.YAW_SERVO_ID] = True
                self.get_logger().info(f"偏航舵机 (ID {self.YAW_SERVO_ID}) 现已在线")
        else:
            self.servo_connection_status[self.YAW_SERVO_ID] = False
            self.get_logger().error(f"偏航舵机 (ID {self.YAW_SERVO_ID}) 无响应!")
    
    def build_command(self, cmd_id, content):
        """构建符合协议的指令包"""
        header = b'\x12\x4C'
        length = len(content).to_bytes(1, 'little')
        cmd_id_byte = cmd_id.to_bytes(1, 'little')
        
        # 构建基础帧
        frame = header + cmd_id_byte + length + content
        
        # 计算校验和 (所有字节求和取低8位)
        checksum = sum(frame) & 0xFF
        frame += checksum.to_bytes(1, 'little')
        
        return frame
    
    def ping_servo(self, servo_id):
        """发送PING指令检查舵机是否在线"""
        with self.serial_lock:
            content = servo_id.to_bytes(1, 'little')
            command = self.build_command(0x01, content)  # PING指令
            
            try:
                self.serial_port.write(command)
                time.sleep(0.1)
                
                # 读取响应 (预期6字节: 05 1C 01 01 [ID] [Checksum])
                response = self.serial_port.read(6)
                if len(response) >= 6 and response[0] == 0x05 and response[1] == 0x1C:
                    # 验证响应的舵机ID
                    response_id = response[4]
                    if response_id == servo_id:
                        return True
                return False
            except Exception as e:
                self.get_logger().error(f"PING错误: {str(e)}")
                return False
    
    def set_servo_angle(self, servo_id, angle):
        """设置舵机角度 (指令ID 8) - 增强版本带响应验证"""
        # 根据舵机ID应用不同的角度限制
        if servo_id == self.PITCH_SERVO_ID:  # 俯仰舵机
            angle = max(self.pitch_min_angle, min(self.pitch_max_angle, angle))
        elif servo_id == self.YAW_SERVO_ID:  # 偏航舵机
            angle = max(self.yaw_min_angle, min(self.yaw_max_angle, angle))
        else:
            # 默认限制
            angle = max(-180.0, min(180.0, angle))

        # 转换为0.1度单位的有符号整数
        angle_int = int(angle * 10)

        # 构建内容: [servo_id, angle(2字节小端有符号), move_time(2字节小端), power(2字节)]
        content = servo_id.to_bytes(1, 'little')
        content += angle_int.to_bytes(2, 'little', signed=True)
        content += self.default_move_time.to_bytes(2, 'little')
        content += self.default_power.to_bytes(2, 'little')  # 使用配置的功率

        with self.serial_lock:
            command = self.build_command(0x08, content)  # MOVE_ON_ANGLE_MODE指令

            try:
                # 清空输入缓冲区，确保读取到的是最新响应
                self.serial_port.reset_input_buffer()

                # 发送命令
                bytes_written = self.serial_port.write(command)
                if bytes_written != len(command):
                    self.get_logger().error(f"命令发送不完整: 期望{len(command)}字节，实际{bytes_written}字节")
                    return False

                # 等待舵机处理命令
                time.sleep(0.01)  # 10ms等待时间

                # 尝试读取响应（可选，某些舵机可能不返回响应）
                try:
                    response = self.serial_port.read(6)  # 尝试读取标准响应
                    if len(response) > 0:
                        self.get_logger().debug(f"舵机 ID {servo_id} 响应: {response.hex(' ')}")
                except:
                    pass  # 忽略读取响应的错误，因为不是所有舵机都返回响应

                servo_name = "俯仰" if servo_id == self.PITCH_SERVO_ID else "偏航"
                self.get_logger().debug(f"{servo_name}舵机命令已发送到 ID {servo_id}: {command.hex(' ')}")

                return True
            except Exception as e:
                self.get_logger().error(f"发送到 ID {servo_id} 的写入错误: {str(e)}")
                return False
    
    def clear_serial_buffers(self):
        """清理串口缓冲区"""
        try:
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.reset_input_buffer()
                self.serial_port.reset_output_buffer()
                self.get_logger().debug("串口缓冲区已清理")
        except Exception as e:
            self.get_logger().error(f"清理缓冲区错误: {str(e)}")

    def recover_connection(self):
        """尝试恢复串口连接"""
        try:
            self.get_logger().info("尝试恢复串口连接...")

            # 关闭现有连接
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                time.sleep(0.5)

            # 重新打开串口
            port = self.get_parameter('port').value
            baudrate = self.get_parameter('baudrate').value

            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.2,
                write_timeout=0.2
            )

            # 清空缓冲区
            self.serial_port.reset_input_buffer()
            self.serial_port.reset_output_buffer()
            time.sleep(0.5)

            # 重新初始化舵机
            self.initialize_servos()
            self.consecutive_failures = 0

            self.get_logger().info("串口连接恢复成功")

        except Exception as e:
            self.get_logger().error(f"连接恢复失败: {str(e)}")

    def cleanup(self):
        """清理资源"""
        self.running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            self.get_logger().info("串口已关闭")

def main():
    rclpy.init()
    node = GimbalServoService()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.cleanup()
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()

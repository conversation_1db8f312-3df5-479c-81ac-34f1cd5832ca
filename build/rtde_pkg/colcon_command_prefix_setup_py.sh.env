AMENT_PREFIX_PATH=/opt/ros/humble
BAMF_DESKTOP_FILE_HINT=/var/lib/snapd/desktop/applications/code_code.desktop
CHROME_DESKTOP=code.desktop
COLCON=1
COLORTERM=truecolor
CONDA_DEFAULT_ENV=base
CONDA_EXE=/home/<USER>/anaconda3/bin/conda
CONDA_PREFIX=/home/<USER>/anaconda3
CONDA_PROMPT_MODIFIER=(base)
CONDA_PYTHON_EXE=/home/<USER>/anaconda3/bin/python
CONDA_SHLVL=1
CUDA_HOME=/usr/local/cuda-12.1
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
DESKTOP_SESSION=ubuntu
DISPLAY=:0
FONTCONFIG_FILE=/etc/fonts/fonts.conf
FONTCONFIG_PATH=/etc/fonts
GDK_BACKEND=x11
GDK_BACKEND_VSCODE_SNAP_ORIG=
GDMSESSION=ubuntu
GIO_LAUNCHED_DESKTOP_FILE=/var/lib/snapd/desktop/applications/code_code.desktop
GIO_LAUNCHED_DESKTOP_FILE_PID=54580
GIO_MODULE_DIR=/home/<USER>/snap/code/common/.cache/gio-modules
GIO_MODULE_DIR_VSCODE_SNAP_ORIG=
GIT_ASKPASS=/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass.sh
GIT_PAGER=cat
GJS_DEBUG_OUTPUT=stderr
GJS_DEBUG_TOPICS=JS ERROR;JS LOG
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
GNOME_SHELL_SESSION_MODE=ubuntu
GPG_AGENT_INFO=/run/user/1000/gnupg/S.gpg-agent:0:1
GSETTINGS_SCHEMA_DIR=/home/<USER>/anaconda3/share/glib-2.0/schemas
GSETTINGS_SCHEMA_DIR_CONDA_BACKUP=/home/<USER>/snap/code/201/.local/share/glib-2.0/schemas
GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG=
GTK_EXE_PREFIX=/snap/code/201/usr
GTK_EXE_PREFIX_VSCODE_SNAP_ORIG=
GTK_IM_MODULE=ibus
GTK_IM_MODULE_FILE=/home/<USER>/snap/code/common/.cache/immodules/immodules.cache
GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG=
GTK_MODULES=gail:atk-bridge
GTK_PATH=/snap/code/201/usr/lib/x86_64-linux-gnu/gtk-3.0
GTK_PATH_VSCODE_SNAP_ORIG=
HOME=/home/<USER>
INVOCATION_ID=0d599cd8298c4bbe9f8e5cb2c79c41da
JOURNAL_STREAM=8:9396
LANG=zh_CN.UTF-8
LANGUAGE=zh_CN:en
LC_ADDRESS=zh_CN.UTF-8
LC_IDENTIFICATION=zh_CN.UTF-8
LC_MEASUREMENT=zh_CN.UTF-8
LC_MONETARY=zh_CN.UTF-8
LC_NAME=zh_CN.UTF-8
LC_NUMERIC=zh_CN.UTF-8
LC_PAPER=zh_CN.UTF-8
LC_TELEPHONE=zh_CN.UTF-8
LC_TIME=zh_CN.UTF-8
LD_LIBRARY_PATH=/usr/local/cuda-12.1/lib64:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64:/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64
LESS=-FX
LESSCLOSE=/usr/bin/lesspipe %s %s
LESSOPEN=| /usr/bin/lesspipe %s
LOCPATH=/snap/code/201/usr/lib/locale
LOCPATH_VSCODE_SNAP_ORIG=
LOGNAME=lwy
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
MANAGERPID=1198
OLDPWD=/home/<USER>/test_ws
ORIGINAL_XDG_CURRENT_DESKTOP=ubuntu:GNOME
PAGER=cat
PAPERSIZE=a4
PATH=/home/<USER>/anaconda3/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools:/home/<USER>/platform-tools-latest-linux/platform-tools
PWD=/home/<USER>/test_ws/build/rtde_pkg
PYTHONPATH=/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages
QT_ACCESSIBILITY=1
QT_IM_MODULE=ibus
ROS_DISTRO=humble
ROS_LOCALHOST_ONLY=0
ROS_PYTHON_VERSION=3
ROS_VERSION=2
SESSION_MANAGER=local/lwy-Laptop:@/tmp/.ICE-unix/1555,unix/lwy-Laptop:/tmp/.ICE-unix/1555
SHELL=/bin/bash
SHLVL=1
SSH_AGENT_LAUNCHER=gnome-keyring
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
SYSTEMD_EXEC_PID=1578
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.102.2
USER=lwy
USERNAME=lwy
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=/snap/code/201/usr/share/code/resources/app/extensions/git/dist/askpass-main.js
VSCODE_GIT_ASKPASS_NODE=/snap/code/201/usr/share/code/code
VSCODE_GIT_IPC_HANDLE=/run/user/1000/vscode-git-1be5817c21.sock
WINDOWPATH=2
XAUTHORITY=/run/user/1000/gdm/Xauthority
XDG_CONFIG_DIRS=/etc/xdg/xdg-ubuntu:/etc/xdg
XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG=/etc/xdg/xdg-ubuntu:/etc/xdg
XDG_CURRENT_DESKTOP=Unity
XDG_DATA_DIRS=/home/<USER>/snap/code/201/.local/share:/home/<USER>/snap/code/201:/snap/code/201/usr/share:/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop
XDG_DATA_DIRS_VSCODE_SNAP_ORIG=/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop
XDG_DATA_HOME=/home/<USER>/snap/code/201/.local/share
XDG_DATA_HOME_VSCODE_SNAP_ORIG=
XDG_MENU_PREFIX=gnome-
XDG_RUNTIME_DIR=/run/user/1000
XDG_SESSION_CLASS=user
XDG_SESSION_DESKTOP=ubuntu
XDG_SESSION_TYPE=x11
XMODIFIERS=@im=ibus
_=/usr/bin/colcon
_CE_CONDA=
_CE_M=

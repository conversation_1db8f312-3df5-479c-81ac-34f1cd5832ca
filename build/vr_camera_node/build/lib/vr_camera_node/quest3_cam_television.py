#!/usr/bin/env python3
"""
Quest 3 High Resolution Camera TeleVision Bridge Ultimate
基于 television_paxini_bridge_ultimate.py 的逻辑
使用 Quest 3 前置相机替代 ZED，支持高分辨率模式
"""

import math
import numpy as np
import sys
import os
import time
from pathlib import Path
from multiprocessing import Array, Process, shared_memory, Queue, Manager, Event, Semaphore
import threading
import cv2

# Add TeleVision to path
sys.path.append('src/inspire_hand_ros2/TeleVision/teleop')
sys.path.append('src/inspire_hand_ros2/ins-dex-retarget')

from TeleVision import OpenTeleVision
from Preprocessor import VuerPreprocessor
from constants_vuer import tip_indices

# ins-dex-retarget imports
from hand_retarget import HandRetarget
from hand_retarget_13dof import HandRetarget13DOF

# ROS2 imports
import rclpy
from rclpy.node import Node
from inspire_hand_interfaces.srv import SetAngle
from rclpy.qos import qos_profile_sensor_data
from geometry_msgs.msg import PoseStamped
from std_msgs.msg import Bool

from pytransform3d import rotations

from my_interfaces.msg import SetJointAngles  # 改为导入消息类型


class Quest3CameraTeleopUltimate:
    """Quest 3 高分辨率相机 TeleVision 桥接器 - 基于原始逻辑"""
    
    def __init__(self, config_file_path, quest_resolution="FHD"):
        # Quest 3 分辨率配置
        self.quest_resolutions = {
            "SD": (640, 480),       # 标清 - 低延迟
            "HD": (1280, 720),      # 高清 - 平衡 (与原始ZED相同)
            "FHD": (1920, 1080),    # 全高清 - 高质量
            "QHD": (2560, 1440)     # 2K - 最高质量
        }
        
        self.quest_resolution_mode = quest_resolution
        
        # 使用与原始代码相同的分辨率设置逻辑
        if quest_resolution == "HD":
            self.resolution = (720, 1280)  # 保持与原始ZED相同
        else:
            self.resolution = self.quest_resolutions[quest_resolution]
        
        print(f"🥽 Quest 3 Camera Mode: {quest_resolution} ({self.resolution[1]}x{self.resolution[0]})")
        
        # 与原始代码完全相同的裁剪设置
        self.crop_size_w = 1  # Updated to match teleop_active_cam.py
        self.crop_size_h = 0
        self.resolution_cropped = (
            self.resolution[0] - self.crop_size_h,
            self.resolution[1] - 2 * self.crop_size_w
        )
        
        self.img_shape = (self.resolution_cropped[0], 2 * self.resolution_cropped[1], 3)
        self.img_height, self.img_width = self.resolution_cropped[:2]
        
        # � 关键：不使用 ZED 相机，直接设置为 None
        self.zed = None
        print("🥽 Using Quest 3 native cameras instead of ZED")
        
        # 创建共享内存用于图像传输 (与原始代码相同)
        self.shm = shared_memory.SharedMemory(create=True, size=np.prod(self.img_shape) * np.uint8().itemsize)
        self.img_array = np.ndarray(self.img_shape, dtype=np.uint8, buffer=self.shm.buf)
        
        # 初始化 Quest 占位图像 (类似原始代码的虚拟图像)
        self._create_quest_placeholder()
        
        image_queue = Queue()
        toggle_streaming = Event()
        
        # 🔑 与原始代码完全相同的 TeleVision 初始化
        self.tv = OpenTeleVision(self.resolution_cropped, self.shm.name, image_queue, toggle_streaming, ngrok=True)
        
        # 与原始代码相同的预处理器和手部重定向
        self.processor = VuerPreprocessor()
        self.hand_retarget13dof = HandRetarget13DOF()
        
        # 启动图像更新线程 (与原始代码相同的逻辑)
        self.image_thread_running = True
        self.image_thread = threading.Thread(target=self.update_images_continuously, daemon=True)
        self.image_thread.start()
    
    def _create_quest_placeholder(self):
        """创建 Quest 占位图像 (替代原始代码的虚拟图像)"""
        left_img = np.zeros((self.img_height, self.img_width, 3), dtype=np.uint8)
        right_img = np.zeros((self.img_height, self.img_width, 3), dtype=np.uint8)
        
        # 添加Quest标识
        font_scale = max(0.5, min(2.0, self.img_width / 800))
        thickness = max(1, int(font_scale * 2))
        
        cv2.putText(left_img, f"Quest 3 {self.quest_resolution_mode}", (30, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), thickness)
        cv2.putText(left_img, f"{self.resolution[1]}x{self.resolution[0]}", (30, 60 + int(40 * font_scale)), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale * 0.8, (255, 255, 0), thickness)
        
        cv2.putText(right_img, f"Quest 3 {self.quest_resolution_mode}", (30, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), thickness)
        cv2.putText(right_img, f"{self.resolution[1]}x{self.resolution[0]}", (30, 60 + int(40 * font_scale)), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale * 0.8, (255, 255, 0), thickness)
        
        combined_image = np.hstack((left_img, right_img))
        np.copyto(self.img_array, combined_image)
    
    def _capture_initial_frame(self):
        """初始帧捕获 - Quest 模式下创建占位图像"""
        self._create_quest_placeholder()
    
    def update_images_continuously(self):
        """连续更新图像数组 - 与原始代码逻辑相同，但使用 Quest 占位图像"""
        frame_count = 0
        
        while self.image_thread_running:
            try:
                # Quest 模式：使用占位图像 (实际视频通过 Quest 直接提供)
                left_img = np.zeros((self.img_height, self.img_width, 3), dtype=np.uint8)
                right_img = np.zeros((self.img_height, self.img_width, 3), dtype=np.uint8)
                
                # 动态状态信息
                timestamp = time.strftime("%H:%M:%S")
                font_scale = max(0.5, min(1.5, self.img_width / 800))
                thickness = max(1, int(font_scale * 2))
                
                cv2.putText(left_img, f"Quest 3 {self.quest_resolution_mode}", (30, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), thickness)
                cv2.putText(left_img, f"Frame: {frame_count}", (30, 60 + int(30 * font_scale)), 
                           cv2.FONT_HERSHEY_SIMPLEX, font_scale * 0.6, (255, 255, 255), thickness)
                cv2.putText(left_img, timestamp, (30, 60 + int(55 * font_scale)), 
                           cv2.FONT_HERSHEY_SIMPLEX, font_scale * 0.6, (255, 255, 255), thickness)
                
                cv2.putText(right_img, f"Quest 3 {self.quest_resolution_mode}", (30, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), thickness)
                cv2.putText(right_img, f"Frame: {frame_count}", (30, 60 + int(30 * font_scale)), 
                           cv2.FONT_HERSHEY_SIMPLEX, font_scale * 0.6, (255, 255, 255), thickness)
                cv2.putText(right_img, timestamp, (30, 60 + int(55 * font_scale)), 
                           cv2.FONT_HERSHEY_SIMPLEX, font_scale * 0.6, (255, 255, 255), thickness)
                
                # 更新共享图像数组
                combined_image = np.hstack((left_img, right_img))
                np.copyto(self.img_array, combined_image)
                
                frame_count += 1
                
                # 根据分辨率调整帧率
                if self.quest_resolution_mode == "QHD":
                    time.sleep(1.0/20.0)  # 20 FPS for 2K
                elif self.quest_resolution_mode == "FHD":
                    time.sleep(1.0/30.0)  # 30 FPS for 1080p
                else:
                    time.sleep(1.0/60.0)  # 60 FPS for HD and below
                
            except Exception as e:
                print(f"❌ Error updating images: {e}")
                # 错误时使用简单占位图像
                try:
                    left_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
                    right_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
                    np.copyto(self.img_array, np.hstack((left_img, right_img)))
                except:
                    pass
                time.sleep(0.1)
    
    def calculate_pinch_distance(self, finger_landmarks):
        if finger_landmarks is None or len(finger_landmarks) < 25:
            return 0.1  # Default distance
            
        # Thumb tip is index 4, index finger tip is index 8 in the landmarks
        thumb_tip = finger_landmarks[4]
        index_tip = finger_landmarks[8]
        
        distance = np.linalg.norm(thumb_tip - index_tip)
        return distance
    
    def create_finger_frames_from_landmarks(self, landmarks, wrist_mat):
        if landmarks is None or len(landmarks) < 25:
            # Return zero frames if no valid data
            return np.zeros((25, 4, 4))
        
        frames = np.zeros((25, 4, 4))
        
        # Set identity matrices as base
        for i in range(25):
            frames[i] = np.eye(4)
            if i < len(landmarks):
                frames[i][:3, 3] = landmarks[i]
        
        return frames
    
    def step(self):
        try:
            # Get preprocessed hand data (following teleop_hand.py pattern)
            process_start = time.time()
            left_wrist_mat, right_wrist_mat, left_hand_landmarks, right_hand_landmarks = self.processor.process_for_controller(self.tv)
            process_time = time.time() - process_start

            # Check if we have valid hand data
            landmarks_valid = False
            if (hasattr(self.tv, 'left_landmarks') and hasattr(self.tv, 'right_landmarks')):
                left_nonzero = np.any(self.tv.left_landmarks != 0)
                right_nonzero = np.any(self.tv.right_landmarks != 0)
                landmarks_valid = left_nonzero or right_nonzero
            
            # landmarks_valid = True
            if landmarks_valid:
                # Calculate pinch distances
                left_pinch_distance = self.calculate_pinch_distance(left_hand_landmarks)
                right_pinch_distance = self.calculate_pinch_distance(right_hand_landmarks)
                
                # Create finger frames for retargeting
                left_finger_frames = self.create_finger_frames_from_landmarks(left_hand_landmarks, left_wrist_mat)
                right_finger_frames = self.create_finger_frames_from_landmarks(right_hand_landmarks, right_wrist_mat)
                
                # Create data structure expected by hand_retarget
                retarget_data = {
                    "left_fingers": left_finger_frames,
                    "right_fingers": right_finger_frames,
                    "left_pinch_distance": left_pinch_distance,
                    "right_pinch_distance": right_pinch_distance
                }
                
                # Get retargeted angles using ins-dex-retarget
                left_angles, right_angles = self.hand_retarget13dof.solve_fingers_angles(retarget_data)

                return {
                    'valid': True,
                    'left_angles': left_angles,
                    'right_angles': right_angles,
                    'left_landmarks': self.tv.left_landmarks,
                    'right_landmarks': self.tv.right_landmarks,
                    'left_hand_landmarks': left_hand_landmarks,
                    'right_hand_landmarks': right_hand_landmarks,
                    'left_wrist_mat':left_wrist_mat,
                    'right_wrist_mat':right_wrist_mat
                }
            else:
                return {
                    'valid': False,
                    'left_angles': np.full(13, 30.0),  # 13个关节角度
                    'right_angles': np.full(13, 30.0), # 13个关节角度
                    'left_landmarks': self.tv.left_landmarks if hasattr(self.tv, 'left_landmarks') else np.zeros((25, 3)),
                    'right_landmarks': self.tv.right_landmarks if hasattr(self.tv, 'right_landmarks') else np.zeros((25, 3)),
                    'left_wrist_mat': np.eye(4),
                    'right_wrist_mat': np.eye(4)
                }
                
        except Exception as e:
            print(f"Error in step: {e}")
            return {
                'valid': False,
                'left_angles': np.full(13, 30.0),  # 13个关节角度
                'right_angles': np.full(13, 30.0), # 13个关节角度
                'left_landmarks': np.zeros((25, 3)),
                'right_landmarks': np.zeros((25, 3)),
                'left_wrist_mat': np.eye(4),
                'right_wrist_mat': np.eye(4)
            }
    
    def cleanup(self):
        """清理资源 - 与原始代码相同"""
        try:
            # Stop image capture thread
            self.image_thread_running = False
            if hasattr(self, 'image_thread'):
                self.image_thread.join(timeout=1.0)
            
            # 不需要关闭 ZED 相机，因为使用 Quest
            
            # Clean up shared memory
            if hasattr(self, 'shm'):
                self.shm.close()
                self.shm.unlink()
                
        except Exception as e:
            print(f"⚠️  Error during cleanup: {e}")


class InspireHandController(Node):
    """与原始代码完全相同的 InspireHandController"""
    
    def __init__(self):
        super().__init__('inspire_hand_controller')

        self.declare_parameter('debugging', False)
        self.declare_parameter('not_use_mcp_abduction', True)

        self.debugging = self.get_parameter('debugging').get_parameter_value().bool_value
        self.not_use_mcp_abduction = self.get_parameter('not_use_mcp_abduction').get_parameter_value().bool_value

        # ROS2 service clients for controlling inspire hands
        self.left_hand_client = self.create_client(SetAngle, '/inspire_hand_left/inspire_hand_set_angle_srv')
        self.right_hand_client = self.create_client(SetAngle, '/inspire_hand_right/inspire_hand_set_angle_srv')
        
        self.paxini_publisher = self.create_publisher(SetJointAngles, '/dexh13_command', 10)
        
        self.start_flag_publisher = self.create_publisher(Bool,"/start_flag",qos_profile_sensor_data)
        self.start_flag = Bool()
        self.start_flag.data = False

        self.left_wrist_pose_publisher = self.create_publisher(PoseStamped,"/left/wrist_pose",qos_profile_sensor_data)
        self.right_wrist_pose_publisher = self.create_publisher(PoseStamped,"/right/wrist_pose",qos_profile_sensor_data)
        
        # Control parameters
        self.left_device_id = 2
        self.right_device_id = 1
    
    def send_hand_commands(self, left_angles, right_angles):
        try:
            # Create Paxini message
            # 13 DOF angles mapping to Paxini hand
            # right_angles structure: [thumb_cmc_abduction, thumb_cmc_flexion, thumb_mcp_flexion, thumb_ip_flexion,
            #                          ring_mcp1_abduction, ring_mcp2_flexion, ring_pip_flexion,
            #                          middle_mcp1_abduction, middle_mcp2_flexion, middle_pip_flexion,
            #                          index_mcp1_abduction, index_mcp2_flexion, index_pip_flexion]
            
            # Direct mapping without normalization since controller accepts 0-90° and -20-20°
            paxini_msg = SetJointAngles()
            
            if left_angles[11] > 80 and left_angles[8] > 80 and left_angles[5] > 80:
                right_angles[2] = 80.0
                right_angles[3] = 80.0
                

            if self.not_use_mcp_abduction:
                # right_angles[0] = -20.0
                right_angles[7] = 0
                right_angles[4] = 0
                right_angles[10] = 0

            if self.debugging:
                # Print the 13DOF angles for debugging
                print(f"📊 13DOF Angles - Right Hand:")
                print(f"   Thumb:  [{right_angles[0]:6.1f}, {right_angles[1]:6.1f}, {right_angles[2]:6.1f}, {right_angles[3]:6.1f}]")
                print(f"   Ring:   [{right_angles[4]:6.1f}, {right_angles[5]:6.1f}, {right_angles[6]:6.1f}]")
                print(f"   Middle: [{right_angles[7]:6.1f}, {right_angles[8]:6.1f}, {right_angles[9]:6.1f}]")
                print(f"   Index:  [{right_angles[10]:6.1f}, {right_angles[11]:6.1f}, {right_angles[12]:6.1f}]")
            
            paxini_msg.joint_angles = [
                # Index finger (3 DOF)
                right_angles[10],  # MCP abduction (-20 to 20)
                right_angles[11],  # MCP flexion (0 to 90) 
                right_angles[12],  # PIP flexion (0 to 90)
                0.0,               # DIP (not controlled)
                
                # Middle finger (3 DOF) 
                right_angles[7],   # MCP abduction (-20 to 20)
                right_angles[8],   # MCP flexion (0 to 90)
                right_angles[9],   # PIP flexion (0 to 90)
                0.0,               # DIP (not controlled)
                
                # Ring finger (3 DOF)
                right_angles[4],   # MCP abduction (-20 to 20)
                right_angles[5],   # MCP flexion (0 to 90)
                right_angles[6],   # PIP flexion (0 to 90)
                0.0,               # DIP (not controlled)
                
                # Thumb (4 DOF)
                right_angles[0],   # CMC abduction (-20 to 20)
                right_angles[1],   # CMC flexion (0 to 90)
                right_angles[2],   # MCP flexion (0 to 90)
                right_angles[3]    # IP flexion (0 to 90)
            ]
            
            self.paxini_publisher.publish(paxini_msg)
            
            return True
            
        except Exception as e:
            self.get_logger().error(f'Error sending hand commands: {str(e)}')
            print(f"❌ ERROR in send_hand_commands: {e}")
            return False
        
    def send_wrist_pose(self, left_wrist_mat, right_wrist_mat):
        
        left_msg = PoseStamped()
        right_msg = PoseStamped()
        
        left_msg = self.wrist_pose_to_msg(left_wrist_mat)
        right_msg = self.wrist_pose_to_msg(right_wrist_mat)
        
        self.left_wrist_pose_publisher.publish(left_msg)
        self.right_wrist_pose_publisher.publish(right_msg)
    
    def wrist_pose_to_msg(self, pose_mat):
        
        msg = PoseStamped()
        
        pose = np.concatenate([pose_mat[:3, 3] + np.array([-0.6, 0, 1.6]),
                            rotations.quaternion_from_matrix(pose_mat[:3, :3])[[1, 2, 3, 0]]])
        
        pose[0] = self.clamp(pose[0],-0.6,-0.05)
        pose[2] = self.clamp(pose[2],0.4,1.5)
        
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.pose.position.x = pose[0]
        msg.pose.position.y = pose[1]
        msg.pose.position.z = pose[2]
        
        msg.pose.orientation.x = pose[3]
        msg.pose.orientation.y = pose[4]
        msg.pose.orientation.z = pose[5]
        msg.pose.orientation.w = pose[6]
        
        return msg
    
    def clamp(self, x, lower_limit, upper_limit):
        if lower_limit > upper_limit:
            lower_limit,upper_limit = upper_limit,lower_limit
        return max(lower_limit, min(x,upper_limit))
    
    def is_pub_start_flag(self, left_angles, right_angles):
        if self.start_flag.data == False:
            left_fingers_closed = (left_angles[5] < 20 and left_angles[8] < 20 and left_angles[11] < 20)
            right_fingers_closed = (right_angles[5] < 20 and right_angles[8] < 20 and right_angles[11] < 20)
            if left_fingers_closed and right_fingers_closed:
                self.start_flag.data = True
        else:
            left_stop = (left_angles[11] < 20 and left_angles[5] > 80 and left_angles[8] > 80)  # 食指伸出，中指无名指收起
            right_stop = (right_angles[11] < 20 and right_angles[5] > 80 and right_angles[8] > 80)
            if left_stop and right_stop:
                self.start_flag.data = False

        self.start_flag_publisher.publish(self.start_flag)


def ros_spin_thread(node):
    while rclpy.ok():
        rclpy.spin_once(node, timeout_sec=0.01)


def main():
    # 🎯 Quest 3 配置参数
    QUEST_RESOLUTION = "FHD"  # 选项: "HD", "FHD", "QHD"
    
    print(f"🚀 Starting Quest 3 TeleVision Bridge Ultimate...")
    print(f"📐 Resolution: {QUEST_RESOLUTION}")
    print("=" * 60)
    
    # Initialize ROS2
    rclpy.init()
    
    # Create inspire hand controller (与原始代码相同)
    hand_controller = InspireHandController()
    
    # Create Quest3 TeleVision bridge (替代 ZED 相机版本)
    config_path = Path("./TeleVision/teleop/inspire_hand.yml")
    teleoperator = Quest3CameraTeleopUltimate(config_path, quest_resolution=QUEST_RESOLUTION)
    
    frame_count = 0
    last_angles_left = None
    last_angles_right = None

    # 启动 ROS 自旋线程 (与原始代码相同)
    spin_thread = threading.Thread(target=ros_spin_thread, args=(hand_controller,))
    spin_thread.daemon = True
    spin_thread.start()
    
    print("🎮 Quest 3 TeleVision Bridge is ready!")
    print("📱 Connect your Quest 3 to start teleoperating")
    print("🛑 Press Ctrl+C to stop")
    
    try:
        while True:
            # Get hand data and retargeted angles + wrist poses (与原始代码相同的逻辑)
            result = teleoperator.step()
            
            frame_count += 1
            
            # Send commands (every frame for maximum responsiveness) - 与原始代码相同
            if result['valid']:
                hand_controller.send_wrist_pose(result['left_wrist_mat'], result['right_wrist_mat'])
                # 1. Update hands (with throttling to reduce ROS2 traffic)
                angle_threshold = 5.0  # 角度变化阈值 (度) - 降低因为现在是角度值而不是0-1000
                
                send_left = True
                send_right = True
                
                if last_angles_left is not None:
                    left_diff = np.abs(np.array(result['left_angles']) - np.array(last_angles_left))
                    send_left = np.any(left_diff > angle_threshold)
                
                if last_angles_right is not None:
                    right_diff = np.abs(np.array(result['right_angles']) - np.array(last_angles_right))
                    send_right = np.any(right_diff > angle_threshold)
                
                if send_left or send_right:
                    # Print angles before sending
                    if send_right:
                        print(f"\n🎯 Sending Right Hand Commands:")
                        print(f"   📐 Raw angles: {result['right_angles']}")
                    
                    success = hand_controller.send_hand_commands(result['left_angles'], result['right_angles'])
                    if success:
                        last_angles_left = result['left_angles'].copy()
                        last_angles_right = result['right_angles'].copy()
            
            # 检查启动标志 (与原始代码相同)
            hand_controller.is_pub_start_flag(result['left_angles'], result['right_angles'])
            
            # Small sleep to prevent excessive CPU usage (与原始代码相同)
            time.sleep(1.0/60.0)  # 60 FPS
            
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        print("\n🛑 Shutting down...")
        teleoperator.cleanup()
        hand_controller.destroy_node()
        rclpy.shutdown()
        print("✅ Shutdown complete. Goodbye!")


if __name__ == "__main__":
    main()

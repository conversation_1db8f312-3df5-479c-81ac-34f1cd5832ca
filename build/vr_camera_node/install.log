/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/quest3_cam_television.py
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/television_paxini_bridge_ultimate.py
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/__init__.py
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/__pycache__/quest3_cam_television.cpython-310.pyc
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/__pycache__/television_paxini_bridge_ultimate.cpython-310.pyc
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node/__pycache__/__init__.cpython-310.pyc
/home/<USER>/test_ws/install/vr_camera_node/share/ament_index/resource_index/packages/vr_camera_node
/home/<USER>/test_ws/install/vr_camera_node/share/vr_camera_node/package.xml
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info/SOURCES.txt
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info/PKG-INFO
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info/requires.txt
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info/dependency_links.txt
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info/zip-safe
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info/top_level.txt
/home/<USER>/test_ws/install/vr_camera_node/lib/python3.10/site-packages/vr_camera_node-0.0.0-py3.10.egg-info/entry_points.txt
/home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node/vr_cam_node
/home/<USER>/test_ws/install/vr_camera_node/lib/vr_camera_node/vr_quest3_cam_node
